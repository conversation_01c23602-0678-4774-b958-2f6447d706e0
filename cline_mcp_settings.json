{"mcpServers": {"github.com/modelcontextprotocol/servers/tree/main/src/brave-search": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-brave-search"], "env": {"BRAVE_API_KEY": "√BSABNny-Cd2VeyCl2wXPpJ1Y0hZaGIl"}, "disabled": false, "autoApprove": []}, "git": {"command": "uvx", "args": ["mcp-server-git"], "disabled": false, "autoApprove": []}, "github": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-github"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "*********************************************************************************************"}, "disabled": false, "autoApprove": []}, "facebook": {"command": "uv", "args": ["--directory", "/path/to/facebook-mcp-server", "run", "facebook-mcp-server"], "disabled": false, "autoApprove": []}, "firecrawl": {"command": "npx", "args": ["-y", "firecrawl-mcp"], "env": {"FIRECRAWL_API_KEY": "fc-eec54b0ff7cd4fc19757ec830790e181"}, "disabled": false, "autoApprove": []}, "github.com/upstash/context7-mcp": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"], "disabled": false, "autoApprove": [], "transportType": "stdio"}, "context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp"]}, "github.com/mendableai/firecrawl-mcp-server": {"command": "npx", "args": ["-y", "firecrawl-mcp"], "env": {"FIRECRAWL_API_KEY": "YOUR_API_KEY"}, "disabled": false, "autoApprove": []}}}