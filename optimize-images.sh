#!/bin/bash

# Image Optimization Script for Barber Brothers Legacy Website
# This script optimizes the 5 gallery images for web performance

echo "🚀 Starting image optimization for Barber Brothers Legacy..."
echo "📊 Original file sizes:"

# Check if ImageMagick is available
if ! command -v convert &> /dev/null; then
    echo "❌ ImageMagick not found. Please install it first:"
    echo "   macOS: brew install imagemagick"
    echo "   Ubuntu: sudo apt-get install imagemagick"
    echo "   Windows: Download from https://imagemagick.org/script/download.php"
    exit 1
fi

# Create optimized directory if it doesn't exist
mkdir -p images/optimized

# Array of gallery images to optimize
declare -a images=(
    "IMG_1073.jpeg"
    "IMG_0920.jpeg" 
    "IMG_0980.jpeg"
    "IMG_0994.jpeg"
    "d1.jpg"
)

# Show original sizes
for img in "${images[@]}"; do
    if [ -f "images/$img" ]; then
        size=$(ls -lh "images/$img" | awk '{print $5}')
        echo "   📸 $img: $size"
    fi
done

echo ""
echo "🔧 Optimizing images..."

# Optimization function
optimize_image() {
    local input="images/$1"
    local output="images/optimized/$1"
    
    if [ ! -f "$input" ]; then
        echo "❌ File not found: $input"
        return 1
    fi
    
    echo "   🔄 Processing $1..."
    
    # Optimize with ImageMagick:
    # - Resize to max 1200px width (maintains aspect ratio)
    # - Reduce quality to 85% (good balance of quality/size)
    # - Strip metadata (EXIF data)
    # - Progressive JPEG for faster loading
    convert "$input" \
        -resize "1200x1200>" \
        -quality 85 \
        -strip \
        -interlace Plane \
        -colorspace sRGB \
        "$output"
    
    if [ $? -eq 0 ]; then
        # Get file sizes for comparison
        original_size=$(stat -f%z "$input" 2>/dev/null || stat -c%s "$input")
        optimized_size=$(stat -f%z "$output" 2>/dev/null || stat -c%s "$output")
        
        # Calculate reduction percentage
        reduction=$(echo "scale=1; (($original_size - $optimized_size) * 100) / $original_size" | bc -l)
        
        # Convert bytes to human readable
        original_mb=$(echo "scale=2; $original_size / 1048576" | bc -l)
        optimized_mb=$(echo "scale=2; $optimized_size / 1048576" | bc -l)
        
        echo "   ✅ $1: ${original_mb}MB → ${optimized_mb}MB (${reduction}% reduction)"
    else
        echo "   ❌ Failed to optimize $1"
    fi
}

# Optimize each image
for img in "${images[@]}"; do
    optimize_image "$img"
done

echo ""
echo "📊 Optimization Summary:"
echo "   📁 Original images: images/"
echo "   📁 Optimized images: images/optimized/"
echo ""
echo "🔄 To use optimized images, run:"
echo "   cp images/optimized/* images/"
echo ""
echo "⚠️  Backup recommendation:"
echo "   mkdir images/original"
echo "   cp images/IMG_*.* images/d1.jpg images/original/"
echo ""
echo "✨ Optimization complete!"
