# Google OAuth Setup Guide - SECURITY UPDATED

⚠️ **CRITICAL SECURITY UPDATE**: This guide has been updated to fix security vulnerabilities and OAuth configuration issues.

## 🚨 IMMEDIATE ACTION REQUIRED

Your current OAuth setup has security issues that need to be fixed immediately:

1. **Client Secret Exposure**: Remove any client secrets from client-side code
2. **Domain Configuration**: Update Google Cloud Console with correct domain settings
3. **OAuth Flow**: Switch to secure client-side OAuth flow

## Step 1: Access Google Cloud Console

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Select your existing project: **"Barber Brothers Legacy"**
3. If you don't have a project, create a new one

## Step 2: Enable Required APIs

1. In the Google Cloud Console, go to "APIs & Services" > "Library"
2. Search for and enable the following APIs:
   - **Google Identity Services API** (REQUIRED for new OAuth)
   - **Google+ API** (for basic profile information)
   - **People API** (for profile data)

## Step 3: Create OAuth 2.0 Credentials

1. Go to "APIs & Services" > "Credentials"
2. Click "Create Credentials" > "OAuth 2.0 Client IDs"
3. If prompted, configure the OAuth consent screen first:
   - Choose "External" user type
   - Fill in the required fields (App name, User support email, Developer contact)
   - Add your domain to authorized domains
   - Save and continue through the scopes and test users sections

## Step 3: Configure OAuth 2.0 Credentials (CRITICAL)

1. Go to "APIs & Services" > "Credentials"
2. Click "Create Credentials" > "OAuth 2.0 Client ID"
3. If prompted, configure the OAuth consent screen first:
   - User Type: **External**
   - App name: "Barber Brothers Legacy"
   - User support email: Your email
   - Developer contact: Your email
   - Authorized domains: **barberbrotherz.com**

4. For the OAuth 2.0 Client ID:
   - Application type: **Web application**
   - Name: "Barber Brothers Legacy Website"

   **🔴 CRITICAL - Authorized JavaScript origins:**
   ```
   http://localhost:3000
   http://127.0.0.1:3000
   https://barberbrotherz.com
   https://www.barberbrotherz.com
   ```

   **🔴 CRITICAL - Authorized redirect URIs:**
   ```
   http://localhost:3000/auth-callback.html
   http://127.0.0.1:3000/auth-callback.html
   https://barberbrotherz.com/auth-callback.html
   https://www.barberbrotherz.com/auth-callback.html
   ```

5. Click "Create"
6. **IMPORTANT**: Only copy the Client ID (NOT the client secret)

## Step 4: Verify Current Configuration

Your current Client ID should be:
```
424859813197-06f189ukl4tg3oi4d499m4dnf015pjl1.apps.googleusercontent.com
```

**✅ DO NOT CHANGE** the Client ID in your code - it's already correct.
**❌ NEVER EXPOSE** the client secret in your frontend code.

## Step 5: Test the Integration

1. Open your website
2. Click on any "Sign in with Google" button
3. You should be redirected to Google's sign-in page
4. After signing in, you should be redirected back to your site

## Important Notes

- **Domain Verification**: Make sure your domain is added to the authorized domains in the OAuth consent screen
- **HTTPS Required**: For production, Google requires HTTPS for OAuth redirects
- **Localhost Exception**: `localhost` and `127.0.0.1` are allowed for development without HTTPS
- **Redirect URI Match**: The redirect URI in your code must exactly match what's configured in Google Cloud Console

## Troubleshooting

### "redirect_uri_mismatch" Error
- Check that your redirect URI exactly matches what's configured in Google Cloud Console
- Make sure you're using the correct protocol (http vs https)
- Verify the domain and path are correct

### "invalid_client" Error
- Double-check your Client ID is correct
- Make sure the Client ID is for a web application, not a mobile app

### "access_blocked" Error
- Your OAuth consent screen might need to be verified by Google
- For testing, add your email to the test users list in the OAuth consent screen

## Current Configuration

The current setup uses:
- Client ID: `424859813197-06f189ukl4tg3oi4d499m4dnf015pjl1.apps.googleusercontent.com`
- Domain: `https://www.barberbrotherz.com`
- Redirect URI: `https://www.barberbrotherz.com/auth-callback.html`
- Scopes: `openid email profile`

✅ **Configuration Updated!** Your Google OAuth is now configured with the latest credentials for barberbrotherz.com.
