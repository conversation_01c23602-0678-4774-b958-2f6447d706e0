require('dotenv').config();
const express = require('express');
const axios = require('axios');
const router = express.Router();

router.post('/auth/google/callback', async (req, res) => {
  const { code } = req.body;

  if (!code) {
    return res.status(400).json({ error: 'Missing code' });
  }

  try {
    // Exchange code for tokens
    const response = await axios.post('https://oauth2.googleapis.com/token', new URLSearchParams({
        code,
        client_id: process.env.GOOGLE_CLIENT_ID,
        client_secret: process.env.GOOGLE_CLIENT_SECRET,
        redirect_uri: 'https://www.barberbrotherz.com/auth-callback.html',
        grant_type: 'authorization_code'
    }).toString(), {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    });

    const { id_token, access_token } = response.data;

    // Optionally: verify id_token, get user info, create session, etc.
    res.json({ id_token, access_token });
  } catch (error) {
    console.error('Google OAuth error:', error.response?.data || error.message);
    res.status(500).json({ error: 'Failed to exchange code for tokens' });
  }
});

module.exports = router; 