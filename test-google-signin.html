<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google Sign-in Test - Barber Brothers Legacy</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background-color: #121212;
            color: white;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #333;
            border-radius: 10px;
        }
        .service-card {
            background: #1a1a1a;
            border: 2px solid #dc3545;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: transform 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        .service-card:hover {
            transform: translateY(-5px);
        }
        .service-icon {
            font-size: 3rem;
            color: #dc3545;
            margin-bottom: 15px;
        }
        .service-overlay {
            position: absolute;
            bottom: -40px;
            left: 0;
            right: 0;
            background: rgba(220, 53, 69, 0.9);
            color: white;
            padding: 10px;
            text-align: center;
            transition: bottom 0.3s ease;
        }
        .service-card:hover .service-overlay {
            bottom: 0;
        }
        .success {
            color: #28a745;
        }
        .error {
            color: #dc3545;
        }
        .user-info {
            background: #1a1a1a;
            border: 1px solid #333;
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Google Sign-in Test - Barber Brothers Legacy</h1>
        
        <div class="test-section">
            <h2>Authentication Status</h2>
            <div id="auth-status">
                <p>Checking authentication status...</p>
            </div>
            <div id="user-info" class="user-info d-none">
                <!-- User info will be displayed here -->
            </div>
            <button id="sign-out-btn" class="btn btn-outline-danger d-none">Sign Out</button>
        </div>
        
        <div class="test-section">
            <h2>Service Cards (Click to Test Google Sign-in)</h2>
            <div class="row">
                <div class="col-md-4 mb-4">
                    <div class="service-card google-signin-btn" data-service="adult">
                        <div class="service-icon">
                            <i class="fas fa-cut"></i>
                        </div>
                        <h3>Adult Haircuts</h3>
                        <p>Premium haircuts tailored to your style.</p>
                        <p class="price">$40 - $50</p>
                        <div class="service-overlay">
                            <span>Sign in to book</span>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="service-card google-signin-btn" data-service="kids">
                        <div class="service-icon">
                            <i class="fas fa-child"></i>
                        </div>
                        <h3>Kids Haircuts</h3>
                        <p>Stylish cuts for the little ones.</p>
                        <p class="price">$30 - $35</p>
                        <div class="service-overlay">
                            <span>Sign in to book</span>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="service-card google-signin-btn" data-service="full">
                        <div class="service-icon">
                            <i class="fas fa-spa"></i>
                        </div>
                        <h3>Full Service</h3>
                        <p>Complete grooming experience for adults.</p>
                        <p class="price">$60</p>
                        <div class="service-overlay">
                            <span>Sign in to book</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>Manual Test Buttons</h2>
            <button id="test-firebase" class="btn btn-primary me-2">Test Firebase Connection</button>
            <button id="test-auth" class="btn btn-secondary me-2">Test Auth State</button>
            <button id="manual-signin" class="btn btn-danger">Manual Google Sign-in</button>
        </div>
        
        <div class="test-section">
            <h2>Console Output</h2>
            <div id="console-output" style="background: #000; padding: 10px; border-radius: 5px; font-family: monospace; max-height: 300px; overflow-y: auto;">
                <!-- Console messages will appear here -->
            </div>
        </div>
        
        <div class="test-section">
            <h2>Go to Main Site</h2>
            <a href="index.html" class="btn btn-success">Open Main Website</a>
        </div>
    </div>

    <!-- Firebase Scripts -->
    <script src="https://www.gstatic.com/firebasejs/9.6.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.6.1/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.6.1/firebase-firestore-compat.js"></script>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/service-calculator.js"></script>
    
    <script>
        // Override console.log to display in the page
        const originalLog = console.log;
        const originalError = console.error;
        const consoleOutput = document.getElementById('console-output');
        
        function addToConsole(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? '#ff6b6b' : type === 'warn' ? '#ffd93d' : '#6bcf7f';
            consoleOutput.innerHTML += `<div style="color: ${color};">[${timestamp}] ${message}</div>`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };
        
        // Test functions
        document.getElementById('test-firebase').addEventListener('click', function() {
            if (typeof firebase !== 'undefined') {
                console.log('✓ Firebase is loaded');
                console.log('Firebase version:', firebase.SDK_VERSION);
                if (firebase.apps.length > 0) {
                    console.log('✓ Firebase app is initialized');
                } else {
                    console.log('✗ Firebase app is not initialized');
                }
            } else {
                console.error('✗ Firebase is not loaded');
            }
        });
        
        document.getElementById('test-auth').addEventListener('click', function() {
            if (typeof firebase !== 'undefined' && firebase.auth) {
                const auth = firebase.auth();
                const user = auth.currentUser;
                if (user) {
                    console.log('✓ User is signed in:', user.displayName);
                    updateAuthStatus(user);
                } else {
                    console.log('✗ No user is signed in');
                    updateAuthStatus(null);
                }
            } else {
                console.error('✗ Firebase Auth is not available');
            }
        });
        
        document.getElementById('manual-signin').addEventListener('click', function() {
            console.log('Manual sign-in triggered');
            signInWithGoogle('manual');
        });
        
        document.getElementById('sign-out-btn').addEventListener('click', function() {
            if (typeof firebase !== 'undefined' && firebase.auth) {
                firebase.auth().signOut()
                    .then(() => {
                        console.log('User signed out successfully');
                        updateAuthStatus(null);
                    })
                    .catch((error) => {
                        console.error('Sign out error:', error);
                    });
            }
        });
        
        function updateAuthStatus(user) {
            const authStatus = document.getElementById('auth-status');
            const userInfo = document.getElementById('user-info');
            const signOutBtn = document.getElementById('sign-out-btn');
            
            if (user) {
                authStatus.innerHTML = '<p class="success">✓ User is signed in</p>';
                userInfo.innerHTML = `
                    <h5>User Information:</h5>
                    <p><strong>Name:</strong> ${user.displayName || 'N/A'}</p>
                    <p><strong>Email:</strong> ${user.email || 'N/A'}</p>
                    <p><strong>UID:</strong> ${user.uid}</p>
                    ${user.photoURL ? `<img src="${user.photoURL}" alt="Profile" width="50" height="50" class="rounded-circle">` : ''}
                `;
                userInfo.classList.remove('d-none');
                signOutBtn.classList.remove('d-none');
            } else {
                authStatus.innerHTML = '<p class="error">✗ No user is signed in</p>';
                userInfo.classList.add('d-none');
                signOutBtn.classList.add('d-none');
            }
        }
        
        // Monitor auth state changes
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Test page loaded');
            
            setTimeout(() => {
                if (typeof firebase !== 'undefined' && firebase.auth) {
                    firebase.auth().onAuthStateChanged((user) => {
                        console.log('Auth state changed:', user ? user.displayName : 'signed out');
                        updateAuthStatus(user);
                    });
                }
            }, 1000);
        });
    </script>
</body>
</html>
