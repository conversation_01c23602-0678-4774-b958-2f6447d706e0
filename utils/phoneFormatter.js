/**
 * Formats a phone number to E.164 format (+1XXXXXXXXXX)
 * @param {string} phone - The phone number to format
 * @returns {string} - Formatted phone number
 */
function formatPhoneNumber(phone) {
  if (!phone) return '';
  
  // Remove all non-digit characters
  const cleaned = phone.replace(/\D/g, '');
  
  // Check if it already has country code
  if (cleaned.length === 10) {
    return `+1${cleaned}`;
  } else if (cleaned.length === 11 && cleaned.startsWith('1')) {
    return `+${cleaned}`;
  } else if (cleaned.length > 11 && cleaned.startsWith('1')) {
    return `+${cleaned.substring(0, 11)}`;
  }
  
  // Default case - just add +1 (might not be valid)
  return `+1${cleaned}`;
}

module.exports = { formatPhoneNumber };