const braveSearch = require('./braveSearch');
require('dotenv').config();

async function test() {
  try {
    const results = await braveSearch('site:barberbrothers.com');
    console.log('Site search results:', JSON.stringify(results, null, 2));
  } catch (error) {
    console.error('Site search error:', error.message);
    // Try a general query to see if the API works at all
    try {
      const generalResults = await braveSearch('barber brothers');
      console.log('General search results:', JSON.stringify(generalResults, null, 2));
    } catch (err) {
      console.error('General search error:', err.message);
    }
  }
}

test(); 