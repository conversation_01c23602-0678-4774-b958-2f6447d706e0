const twilio = require('twilio');
require('dotenv').config();

/**
 * Creates and returns a configured Twilio client
 * @returns {Object} Twilio client
 */
function getTwilioClient() {
  const accountSid = process.env.TWILIO_ACCOUNT_SID;
  const authToken = process.env.TWILIO_AUTH_TOKEN;
  
  if (!accountSid || !authToken) {
    console.error('Missing Twilio credentials in environment variables');
    throw new Error('Twilio credentials not configured');
  }
  
  return new twilio(accountSid, authToken, {
    timeout: 30000 // 30 second timeout
  });
}

/**
 * Sends SMS messages to both customer and barber
 * @param {Object} appointment - Appointment details
 * @returns {Promise<Object>} Message SIDs
 */
async function sendAppointmentNotifications(appointment) {
  const { name, phone, service, date, time, notes = '' } = appointment;
  const client = getTwilioClient();
  
  // Format phone number
  const { formatPhoneNumber } = require('../utils/phoneFormatter');
  const formattedPhone = formatPhoneNumber(phone);
  
  try {
    // Send to customer
    const customerMessage = await client.messages.create({
      body: `Hi ${name}, your appointment for ${service} has been confirmed for ${date} at ${time}. See you then! - Barber Brothers Legacy`,
      to: formattedPhone,
      from: process.env.TWILIO_PHONE_NUMBER
    });
    
    // Send to barber
    const barberMessage = await client.messages.create({
      body: `New appointment: ${name} for ${service} on ${date} at ${time}. Customer phone: ${formattedPhone}${notes ? `\nNotes: ${notes}` : ''}`,
      to: process.env.BARBER_PHONE_NUMBER,
      from: process.env.TWILIO_PHONE_NUMBER
    });
    
    return {
      customerMessageSid: customerMessage.sid,
      barberMessageSid: barberMessage.sid
    };
  } catch (error) {
    console.error('Twilio error:', error);
    throw error;
  }
}

module.exports = {
  getTwilioClient,
  sendAppointmentNotifications
};