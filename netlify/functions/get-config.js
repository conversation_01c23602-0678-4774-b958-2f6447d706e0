exports.handler = async function(event, context) {
    // Only allow GET requests
    if (event.httpMethod !== 'GET') {
        return {
            statusCode: 405,
            body: JSON.stringify({ error: 'Method not allowed' })
        };
    }

    // Allow all origins during testing (you can restrict this later)
    const origin = event.headers.origin || '*';

    try {
        return {
            statusCode: 200,
            headers: {
                'Access-Control-Allow-Origin': origin,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                youtubeApiKey: process.env.YOUTUBE_API_KEY,
                youtubeChannelId: process.env.YOUTUBE_CHANNEL_ID || 'YOUR_CHANNEL_ID' // We'll update this later
            })
        };
    } catch (error) {
        return {
            statusCode: 500,
            body: JSON.stringify({ error: 'Failed to load configuration' })
        };
    }
}; 