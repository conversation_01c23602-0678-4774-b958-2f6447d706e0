const twilio = require('twilio');

exports.handler = async function(event, context) {
  // Handle OPTIONS request for CORS
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type',
        'Access-Control-Allow-Methods': 'POST, OPTIONS'
      },
      body: ''
    };
  }

  // Only allow POST
  if (event.httpMethod !== 'POST') {
    return {
      statusCode: 405,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type'
      },
      body: JSON.stringify({ success: false, message: 'Method Not Allowed' })
    };
  }

  try {
    // Parse the incoming request body
    const data = JSON.parse(event.body);
    const { name, phone, service, date, time } = data;

    console.log('Received booking request:', { name, phone, service, date, time });
    console.log('Environment variables:', {
      hasTwilioSid: !!process.env.TWILIO_ACCOUNT_SID,
      hasTwilioToken: !!process.env.TWILIO_AUTH_TOKEN,
      hasTwilioPhone: !!process.env.TWILIO_PHONE_NUMBER,
      hasBarberPhone: !!process.env.BARBER_PHONE_NUMBER
    });

    // Initialize Twilio client
    const client = new twilio(
      process.env.TWILIO_ACCOUNT_SID,
      process.env.TWILIO_AUTH_TOKEN
    );

    // Format phone number to E.164 format
    const formattedPhone = phone.startsWith('+1') ? phone : `+1${phone}`;

    console.log('Sending SMS to customer:', formattedPhone);

    // Send SMS to customer
    const customerMessage = await client.messages.create({
      body: `Hi ${name}, your appointment for ${service} has been confirmed for ${date} at ${time}. See you then! - Barber Brothers Legacy`,
      to: formattedPhone,
      from: process.env.TWILIO_PHONE_NUMBER
    });

    console.log('Customer message sent:', customerMessage.sid);
    console.log('Sending SMS to barber:', process.env.BARBER_PHONE_NUMBER);

    // Send SMS to barber
    const barberMessage = await client.messages.create({
      body: `New appointment: ${name} for ${service} on ${date} at ${time}. Customer phone: ${formattedPhone}`,
      to: process.env.BARBER_PHONE_NUMBER,
      from: process.env.TWILIO_PHONE_NUMBER
    });

    console.log('Barber message sent:', barberMessage.sid);

    return {
      statusCode: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type'
      },
      body: JSON.stringify({
        success: true,
        message: 'Appointment booked successfully!'
      })
    };
  } catch (error) {
    console.error('Error details:', {
      message: error.message,
      code: error.code,
      status: error.status,
      moreInfo: error.moreInfo,
      stack: error.stack
    });

    return {
      statusCode: 500,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type'
      },
      body: JSON.stringify({
        success: false,
        message: 'Failed to send notification. Please try again or call us at (*************.',
        error: error.message
      })
    };
  }
}; 