#!/bin/bash

# Image Optimization Script using macOS built-in sips tool
# This script optimizes the 5 gallery images for web performance

echo "🚀 Starting image optimization using macOS sips..."
echo "📊 Original file sizes:"

# Create optimized directory if it doesn't exist
mkdir -p images/optimized

# Array of gallery images to optimize
declare -a images=(
    "IMG_1073.jpeg"
    "IMG_0920.jpeg" 
    "IMG_0980.jpeg"
    "IMG_0994.jpeg"
    "d1.jpg"
)

# Show original sizes
for img in "${images[@]}"; do
    if [ -f "images/$img" ]; then
        size=$(ls -lh "images/$img" | awk '{print $5}')
        echo "   📸 $img: $size"
    fi
done

echo ""
echo "🔧 Optimizing images with sips..."

# Optimization function using sips
optimize_image() {
    local input="images/$1"
    local output="images/optimized/$1"
    
    if [ ! -f "$input" ]; then
        echo "❌ File not found: $input"
        return 1
    fi
    
    echo "   🔄 Processing $1..."
    
    # Copy original to optimized folder first
    cp "$input" "$output"
    
    # Get original dimensions
    original_width=$(sips -g pixelWidth "$input" | tail -1 | awk '{print $2}')
    original_height=$(sips -g pixelHeight "$input" | tail -1 | awk '{print $2}')
    
    # Calculate new dimensions (max width 1200px, maintain aspect ratio)
    if [ "$original_width" -gt 1200 ]; then
        new_width=1200
        new_height=$(echo "scale=0; $original_height * 1200 / $original_width" | bc)
        
        # Resize the image
        sips -z "$new_height" "$new_width" "$output" > /dev/null 2>&1
        echo "   📏 Resized from ${original_width}x${original_height} to ${new_width}x${new_height}"
    else
        echo "   📏 Image already optimal size: ${original_width}x${original_height}"
    fi
    
    # Set JPEG quality to 85%
    if [[ "$1" == *.jpg ]] || [[ "$1" == *.jpeg ]]; then
        sips -s formatOptions 85 "$output" > /dev/null 2>&1
    fi
    
    # Get file sizes for comparison
    original_size=$(stat -f%z "$input" 2>/dev/null)
    optimized_size=$(stat -f%z "$output" 2>/dev/null)
    
    if [ "$optimized_size" -lt "$original_size" ]; then
        # Calculate reduction percentage
        reduction=$(echo "scale=1; (($original_size - $optimized_size) * 100) / $original_size" | bc -l)
        
        # Convert bytes to human readable
        original_mb=$(echo "scale=2; $original_size / 1048576" | bc -l)
        optimized_mb=$(echo "scale=2; $optimized_size / 1048576" | bc -l)
        
        echo "   ✅ $1: ${original_mb}MB → ${optimized_mb}MB (${reduction}% reduction)"
    else
        echo "   ℹ️  $1: No size reduction achieved"
    fi
}

# Optimize each image
for img in "${images[@]}"; do
    optimize_image "$img"
done

echo ""
echo "📊 Final optimized sizes:"
for img in "${images[@]}"; do
    if [ -f "images/optimized/$img" ]; then
        size=$(ls -lh "images/optimized/$img" | awk '{print $5}')
        echo "   📸 $img: $size"
    fi
done

echo ""
echo "📊 Optimization Summary:"
echo "   📁 Original images: images/"
echo "   📁 Optimized images: images/optimized/"
echo ""
echo "🔄 To use optimized images, run:"
echo "   # Backup originals first"
echo "   mkdir -p images/original"
echo "   cp images/IMG_*.* images/d1.jpg images/original/"
echo "   # Replace with optimized versions"
echo "   cp images/optimized/* images/"
echo ""
echo "✨ Optimization complete!"
