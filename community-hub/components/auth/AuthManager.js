/**
 * Authentication Manager Component
 * Handles authentication UI and user session management
 */

import AuthService from '../../services/AuthService.js';

class AuthManager {
    constructor() {
        this.authService = AuthService;
        this.isInitialized = false;
        this.authStateUnsubscribe = null;
        
        this.init();
    }

    /**
     * Initialize the authentication manager
     */
    async init() {
        try {
            console.log('🔐 AuthManager: Initializing...');
            
            // Wait for AuthService to be ready
            await this.waitForAuthService();
            
            // Setup auth state listener
            this.setupAuthStateListener();
            
            // Setup UI event listeners
            this.setupEventListeners();
            
            this.isInitialized = true;
            console.log('✅ AuthManager: Initialized successfully');
            
        } catch (error) {
            console.error('❌ AuthManager: Initialization failed:', error);
        }
    }

    /**
     * Wait for AuthService to be initialized
     */
    async waitForAuthService() {
        return new Promise((resolve) => {
            const checkInterval = setInterval(() => {
                if (this.authService.isInitialized) {
                    clearInterval(checkInterval);
                    resolve();
                }
            }, 100);
        });
    }

    /**
     * Setup authentication state listener
     */
    setupAuthStateListener() {
        this.authStateUnsubscribe = this.authService.onAuthStateChanged((user) => {
            this.handleAuthStateChange(user);
        });
    }

    /**
     * Handle authentication state changes
     */
    handleAuthStateChange(user) {
        console.log('🔄 AuthManager: Auth state changed:', user ? user.displayName : 'No user');
        
        if (user) {
            this.showAuthenticatedUI(user);
            this.hideAuthenticationPrompts();
        } else {
            this.showUnauthenticatedUI();
            this.showAuthenticationPrompts();
        }
        
        // Dispatch custom event for other components
        window.dispatchEvent(new CustomEvent('authStateChanged', {
            detail: { user }
        }));
    }

    /**
     * Show UI for authenticated users
     */
    showAuthenticatedUI(user) {
        // Update user menu in navbar
        this.updateUserMenu(user);
        
        // Show authenticated sections
        const authSections = document.querySelectorAll('.auth-required');
        authSections.forEach(section => {
            section.style.display = 'block';
        });
        
        // Hide sign-in prompts
        const signInPrompts = document.querySelectorAll('.sign-in-prompt');
        signInPrompts.forEach(prompt => {
            prompt.style.display = 'none';
        });
        
        // Update community hub access
        this.enableCommunityFeatures();
    }

    /**
     * Show UI for unauthenticated users
     */
    showUnauthenticatedUI() {
        // Hide user menu
        const userMenu = document.getElementById('user-menu');
        if (userMenu) {
            userMenu.classList.add('d-none');
        }
        
        // Show sign-in menu
        const signInMenu = document.getElementById('signin-menu');
        if (signInMenu) {
            signInMenu.classList.remove('d-none');
        }
        
        // Hide authenticated sections
        const authSections = document.querySelectorAll('.auth-required');
        authSections.forEach(section => {
            section.style.display = 'none';
        });
        
        // Disable community features
        this.disableCommunityFeatures();
    }

    /**
     * Update user menu with current user info
     */
    updateUserMenu(user) {
        const userMenu = document.getElementById('user-menu');
        const signInMenu = document.getElementById('signin-menu');
        const userAvatar = document.getElementById('user-avatar');
        const userName = document.getElementById('user-name');
        
        if (userMenu && signInMenu) {
            // Show user menu, hide sign-in menu
            userMenu.classList.remove('d-none');
            signInMenu.classList.add('d-none');
            
            // Update user info
            if (userAvatar) {
                userAvatar.src = user.photoURL || '/images/time.jpeg';
                userAvatar.alt = user.displayName || 'User';
            }
            
            if (userName) {
                userName.textContent = user.displayName || 'User';
            }
        }
    }

    /**
     * Enable community features for authenticated users
     */
    enableCommunityFeatures() {
        // Enable post creation
        const postComposer = document.querySelector('.post-composer');
        if (postComposer) {
            postComposer.classList.remove('disabled');
        }
        
        // Enable interaction buttons
        const interactionButtons = document.querySelectorAll('.interaction-btn');
        interactionButtons.forEach(btn => {
            btn.disabled = false;
            btn.classList.remove('disabled');
        });
        
        // Show community navigation
        const communityNav = document.querySelector('.community-nav');
        if (communityNav) {
            communityNav.style.display = 'block';
        }
    }

    /**
     * Disable community features for unauthenticated users
     */
    disableCommunityFeatures() {
        // Disable post creation
        const postComposer = document.querySelector('.post-composer');
        if (postComposer) {
            postComposer.classList.add('disabled');
        }
        
        // Disable interaction buttons
        const interactionButtons = document.querySelectorAll('.interaction-btn');
        interactionButtons.forEach(btn => {
            btn.disabled = true;
            btn.classList.add('disabled');
        });
    }

    /**
     * Show authentication prompts
     */
    showAuthenticationPrompts() {
        const signInPrompts = document.querySelectorAll('.sign-in-prompt');
        signInPrompts.forEach(prompt => {
            prompt.style.display = 'block';
        });
    }

    /**
     * Hide authentication prompts
     */
    hideAuthenticationPrompts() {
        const signInPrompts = document.querySelectorAll('.sign-in-prompt');
        signInPrompts.forEach(prompt => {
            prompt.style.display = 'none';
        });
    }

    /**
     * Setup event listeners for authentication actions
     */
    setupEventListeners() {
        // Sign in button clicks
        document.addEventListener('click', (e) => {
            if (e.target.matches('.google-signin-btn, .community-signin-btn')) {
                e.preventDefault();
                this.handleSignInClick();
            }
            
            if (e.target.matches('#sign-out-link')) {
                e.preventDefault();
                this.handleSignOutClick();
            }
        });
        
        // Handle return from OAuth
        this.handleOAuthReturn();
    }

    /**
     * Handle sign in button click
     */
    async handleSignInClick() {
        try {
            console.log('🔐 AuthManager: Sign in requested');
            
            // Show loading state
            this.showSignInLoading();
            
            await this.authService.signInWithGoogle();
            
        } catch (error) {
            console.error('❌ AuthManager: Sign in failed:', error);
            this.showSignInError(error.message);
        } finally {
            this.hideSignInLoading();
        }
    }

    /**
     * Handle sign out button click
     */
    async handleSignOutClick() {
        try {
            console.log('🔐 AuthManager: Sign out requested');
            
            await this.authService.signOut();
            
            // Show success message
            this.showMessage('You have been signed out successfully.', 'success');
            
        } catch (error) {
            console.error('❌ AuthManager: Sign out failed:', error);
            this.showMessage('Sign out failed. Please try again.', 'error');
        }
    }

    /**
     * Handle OAuth return from main site
     */
    handleOAuthReturn() {
        // Check if we're returning from OAuth
        const authRedirectSource = localStorage.getItem('authRedirectSource');
        if (authRedirectSource === 'community-hub') {
            localStorage.removeItem('authRedirectSource');
            
            // Check for successful authentication
            const authSuccess = localStorage.getItem('authSuccess');
            if (authSuccess === 'true') {
                console.log('✅ AuthManager: OAuth return successful');
                this.showMessage('Welcome to the Barber Brothers Community!', 'success');
            }
        }
    }

    /**
     * Show sign in loading state
     */
    showSignInLoading() {
        const signInBtns = document.querySelectorAll('.google-signin-btn, .community-signin-btn');
        signInBtns.forEach(btn => {
            btn.disabled = true;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Signing in...';
        });
    }

    /**
     * Hide sign in loading state
     */
    hideSignInLoading() {
        const signInBtns = document.querySelectorAll('.google-signin-btn, .community-signin-btn');
        signInBtns.forEach(btn => {
            btn.disabled = false;
            btn.innerHTML = '<i class="fab fa-google me-2"></i>Sign In';
        });
    }

    /**
     * Show sign in error
     */
    showSignInError(message) {
        this.showMessage(`Sign in failed: ${message}`, 'error');
    }

    /**
     * Show message to user
     */
    showMessage(message, type = 'info') {
        // Create toast notification
        const toast = document.createElement('div');
        toast.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        toast.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(toast);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 5000);
    }

    /**
     * Get current user
     */
    getCurrentUser() {
        return this.authService.getCurrentUser();
    }

    /**
     * Check if user is authenticated
     */
    isAuthenticated() {
        return this.authService.isAuthenticated();
    }

    /**
     * Cleanup
     */
    destroy() {
        if (this.authStateUnsubscribe) {
            this.authStateUnsubscribe();
        }
    }
}

// Export singleton instance
export default new AuthManager();
