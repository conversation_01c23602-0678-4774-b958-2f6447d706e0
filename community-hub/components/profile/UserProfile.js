/**
 * User Profile Component
 * Displays user profile information and manages profile interactions
 */

import AuthService from '../../services/AuthService.js';

class UserProfile {
    constructor(userId = null) {
        this.userId = userId;
        this.user = null;
        this.currentUser = AuthService.getCurrentUser();
        this.isOwnProfile = false;
        this.container = null;
        
        this.init();
    }

    /**
     * Initialize the user profile component
     */
    async init() {
        try {
            console.log('👤 UserProfile: Initializing for user:', this.userId);
            
            // If no userId provided, show current user's profile
            if (!this.userId && this.currentUser) {
                this.userId = this.currentUser.id;
            }
            
            if (!this.userId) {
                throw new Error('No user ID provided and no authenticated user');
            }
            
            // Check if this is the current user's own profile
            this.isOwnProfile = this.currentUser && this.currentUser.id === this.userId;
            
            // Load user data
            await this.loadUserData();
            
            // Render the profile
            this.render();
            
            // Setup event listeners
            this.setupEventListeners();
            
            console.log('✅ UserProfile: Initialized successfully');
            
        } catch (error) {
            console.error('❌ UserProfile: Initialization failed:', error);
            this.renderError(error.message);
        }
    }

    /**
     * Load user data from database
     */
    async loadUserData() {
        try {
            if (this.isOwnProfile && this.currentUser) {
                this.user = this.currentUser;
            } else {
                this.user = await AuthService.getUserById(this.userId);
            }
            
            if (!this.user) {
                throw new Error('User not found');
            }
            
            console.log('✅ UserProfile: User data loaded:', this.user.displayName);
            
        } catch (error) {
            console.error('❌ UserProfile: Failed to load user data:', error);
            throw error;
        }
    }

    /**
     * Render the user profile
     */
    render() {
        const profileHTML = this.generateProfileHTML();
        
        // Find or create container
        this.container = document.getElementById('user-profile-container') || 
                       document.querySelector('.user-profile-container');
        
        if (!this.container) {
            this.container = document.createElement('div');
            this.container.className = 'user-profile-container';
            document.body.appendChild(this.container);
        }
        
        this.container.innerHTML = profileHTML;
        
        // Add CSS classes for styling
        this.container.classList.add('profile-loaded');
    }

    /**
     * Generate profile HTML
     */
    generateProfileHTML() {
        const isFollowing = this.currentUser && 
                           this.user.followers && 
                           this.user.followers.includes(this.currentUser.id);
        
        const followersCount = this.user.followers ? this.user.followers.length : 0;
        const followingCount = this.user.following ? this.user.following.length : 0;
        
        return `
            <div class="profile-header">
                <div class="profile-cover">
                    <img src="${this.user.coverPhoto || '/images/barber-cover-default.jpg'}" 
                         alt="Cover Photo" class="cover-image">
                </div>
                
                <div class="profile-info">
                    <div class="profile-avatar">
                        <img src="${this.user.photoURL || '/images/time.jpeg'}" 
                             alt="${this.user.displayName}" class="avatar-image">
                        ${this.user.isVerified ? '<div class="verified-badge"><i class="fas fa-check-circle"></i></div>' : ''}
                        ${this.user.role === 'barber' ? '<div class="barber-badge"><i class="fas fa-cut"></i></div>' : ''}
                    </div>
                    
                    <div class="profile-details">
                        <h1 class="profile-name">
                            ${this.user.displayName}
                            ${this.user.isVerified ? '<i class="fas fa-check-circle text-primary ms-2"></i>' : ''}
                        </h1>
                        
                        ${this.user.bio ? `<p class="profile-bio">${this.escapeHtml(this.user.bio)}</p>` : ''}
                        
                        <div class="profile-meta">
                            ${this.user.location ? `<span class="location"><i class="fas fa-map-marker-alt me-1"></i>${this.escapeHtml(this.user.location)}</span>` : ''}
                            <span class="join-date"><i class="fas fa-calendar me-1"></i>Joined ${this.formatDate(this.user.joinDate)}</span>
                            ${this.user.role === 'barber' ? '<span class="role-badge"><i class="fas fa-cut me-1"></i>Professional Barber</span>' : ''}
                        </div>
                        
                        <div class="profile-stats">
                            <div class="stat">
                                <span class="stat-number">${this.user.postsCount || 0}</span>
                                <span class="stat-label">Posts</span>
                            </div>
                            <div class="stat">
                                <span class="stat-number">${followersCount}</span>
                                <span class="stat-label">Followers</span>
                            </div>
                            <div class="stat">
                                <span class="stat-number">${followingCount}</span>
                                <span class="stat-label">Following</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="profile-actions">
                        ${this.generateActionButtons(isFollowing)}
                    </div>
                </div>
            </div>
            
            <div class="profile-content">
                <div class="profile-tabs">
                    <button class="tab-btn active" data-tab="posts">Posts</button>
                    <button class="tab-btn" data-tab="about">About</button>
                    ${this.isOwnProfile ? '<button class="tab-btn" data-tab="settings">Settings</button>' : ''}
                </div>
                
                <div class="tab-content">
                    <div class="tab-pane active" id="posts-tab">
                        <div class="posts-container">
                            <div class="loading-posts">
                                <i class="fas fa-spinner fa-spin"></i>
                                <span>Loading posts...</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="tab-pane" id="about-tab">
                        ${this.generateAboutTab()}
                    </div>
                    
                    ${this.isOwnProfile ? `
                        <div class="tab-pane" id="settings-tab">
                            ${this.generateSettingsTab()}
                        </div>
                    ` : ''}
                </div>
            </div>
        `;
    }

    /**
     * Generate action buttons based on user relationship
     */
    generateActionButtons(isFollowing) {
        if (this.isOwnProfile) {
            return `
                <button class="btn btn-outline-danger edit-profile-btn">
                    <i class="fas fa-edit me-2"></i>Edit Profile
                </button>
            `;
        }
        
        if (!this.currentUser) {
            return `
                <button class="btn btn-danger community-signin-btn">
                    <i class="fas fa-sign-in-alt me-2"></i>Sign In to Follow
                </button>
            `;
        }
        
        return `
            <button class="btn ${isFollowing ? 'btn-outline-danger' : 'btn-danger'} follow-btn" 
                    data-user-id="${this.userId}" 
                    data-following="${isFollowing}">
                <i class="fas ${isFollowing ? 'fa-user-minus' : 'fa-user-plus'} me-2"></i>
                ${isFollowing ? 'Unfollow' : 'Follow'}
            </button>
            <button class="btn btn-outline-secondary message-btn" data-user-id="${this.userId}">
                <i class="fas fa-envelope me-2"></i>Message
            </button>
        `;
    }

    /**
     * Generate about tab content
     */
    generateAboutTab() {
        return `
            <div class="about-content">
                <div class="about-section">
                    <h3>About ${this.user.displayName}</h3>
                    <p>${this.user.bio || 'No bio available.'}</p>
                </div>
                
                <div class="about-section">
                    <h3>Details</h3>
                    <div class="detail-item">
                        <strong>Location:</strong> ${this.user.location || 'Not specified'}
                    </div>
                    <div class="detail-item">
                        <strong>Member since:</strong> ${this.formatDate(this.user.joinDate)}
                    </div>
                    <div class="detail-item">
                        <strong>Role:</strong> ${this.user.role === 'barber' ? 'Professional Barber' : 'Community Member'}
                    </div>
                    ${this.user.isVerified ? '<div class="detail-item"><strong>Status:</strong> Verified Member</div>' : ''}
                </div>
                
                ${this.user.preferences && this.user.preferences.privacy === 'public' ? `
                    <div class="about-section">
                        <h3>Activity</h3>
                        <div class="activity-stats">
                            <div class="activity-item">
                                <span class="activity-number">${this.user.postsCount || 0}</span>
                                <span class="activity-label">Posts Created</span>
                            </div>
                            <div class="activity-item">
                                <span class="activity-number">${(this.user.followers || []).length}</span>
                                <span class="activity-label">Followers</span>
                            </div>
                            <div class="activity-item">
                                <span class="activity-number">${(this.user.following || []).length}</span>
                                <span class="activity-label">Following</span>
                            </div>
                        </div>
                    </div>
                ` : ''}
            </div>
        `;
    }

    /**
     * Generate settings tab content (only for own profile)
     */
    generateSettingsTab() {
        if (!this.isOwnProfile) return '';
        
        return `
            <div class="settings-content">
                <div class="settings-section">
                    <h3>Profile Settings</h3>
                    <form class="profile-settings-form">
                        <div class="form-group">
                            <label for="display-name">Display Name</label>
                            <input type="text" id="display-name" class="form-control" 
                                   value="${this.user.displayName}" maxlength="50">
                        </div>
                        
                        <div class="form-group">
                            <label for="bio">Bio</label>
                            <textarea id="bio" class="form-control" rows="3" maxlength="160"
                                      placeholder="Tell us about yourself...">${this.user.bio || ''}</textarea>
                        </div>
                        
                        <div class="form-group">
                            <label for="location">Location</label>
                            <input type="text" id="location" class="form-control" 
                                   value="${this.user.location || ''}" maxlength="50">
                        </div>
                        
                        <button type="submit" class="btn btn-danger">Save Changes</button>
                    </form>
                </div>
                
                <div class="settings-section">
                    <h3>Privacy Settings</h3>
                    <div class="privacy-options">
                        <div class="form-check">
                            <input type="radio" id="privacy-public" name="privacy" value="public" 
                                   class="form-check-input" ${this.user.preferences?.privacy === 'public' ? 'checked' : ''}>
                            <label for="privacy-public" class="form-check-label">
                                <strong>Public</strong> - Anyone can see your profile and posts
                            </label>
                        </div>
                        <div class="form-check">
                            <input type="radio" id="privacy-private" name="privacy" value="private" 
                                   class="form-check-input" ${this.user.preferences?.privacy === 'private' ? 'checked' : ''}>
                            <label for="privacy-private" class="form-check-label">
                                <strong>Private</strong> - Only followers can see your posts
                            </label>
                        </div>
                    </div>
                </div>
                
                <div class="settings-section">
                    <h3>Notification Settings</h3>
                    <div class="notification-options">
                        <div class="form-check form-switch">
                            <input type="checkbox" id="notifications" class="form-check-input" 
                                   ${this.user.preferences?.notifications ? 'checked' : ''}>
                            <label for="notifications" class="form-check-label">Enable notifications</label>
                        </div>
                        <div class="form-check form-switch">
                            <input type="checkbox" id="email-updates" class="form-check-input" 
                                   ${this.user.preferences?.emailUpdates ? 'checked' : ''}>
                            <label for="email-updates" class="form-check-label">Email updates</label>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        if (!this.container) return;
        
        // Tab switching
        this.container.addEventListener('click', (e) => {
            if (e.target.matches('.tab-btn')) {
                this.handleTabSwitch(e.target);
            }
            
            if (e.target.matches('.follow-btn')) {
                this.handleFollowClick(e.target);
            }
            
            if (e.target.matches('.edit-profile-btn')) {
                this.handleEditProfileClick();
            }
        });
        
        // Settings form submission
        this.container.addEventListener('submit', (e) => {
            if (e.target.matches('.profile-settings-form')) {
                e.preventDefault();
                this.handleSettingsSubmit(e.target);
            }
        });
        
        // Privacy and notification changes
        this.container.addEventListener('change', (e) => {
            if (e.target.matches('input[name="privacy"]') || 
                e.target.matches('#notifications, #email-updates')) {
                this.handleSettingsChange(e.target);
            }
        });
    }

    /**
     * Handle tab switching
     */
    handleTabSwitch(tabBtn) {
        // Remove active class from all tabs and panes
        this.container.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
        this.container.querySelectorAll('.tab-pane').forEach(pane => pane.classList.remove('active'));
        
        // Add active class to clicked tab
        tabBtn.classList.add('active');
        
        // Show corresponding pane
        const tabId = tabBtn.dataset.tab;
        const pane = this.container.querySelector(`#${tabId}-tab`);
        if (pane) {
            pane.classList.add('active');
            
            // Load content if needed
            if (tabId === 'posts') {
                this.loadUserPosts();
            }
        }
    }

    /**
     * Load user posts (placeholder for now)
     */
    loadUserPosts() {
        const postsContainer = this.container.querySelector('.posts-container');
        if (postsContainer) {
            // This would integrate with the PostService
            postsContainer.innerHTML = '<p>Posts will be loaded here...</p>';
        }
    }

    /**
     * Handle follow/unfollow click
     */
    async handleFollowClick(button) {
        // This would integrate with the FollowService
        console.log('Follow/unfollow clicked for user:', button.dataset.userId);
    }

    /**
     * Handle edit profile click
     */
    handleEditProfileClick() {
        // Switch to settings tab
        const settingsTab = this.container.querySelector('[data-tab="settings"]');
        if (settingsTab) {
            this.handleTabSwitch(settingsTab);
        }
    }

    /**
     * Handle settings form submission
     */
    async handleSettingsSubmit(form) {
        try {
            const formData = new FormData(form);
            const updates = {
                displayName: formData.get('display-name') || this.user.displayName,
                bio: formData.get('bio') || '',
                location: formData.get('location') || ''
            };
            
            await AuthService.updateUserProfile(updates);
            
            // Update local user data
            Object.assign(this.user, updates);
            
            // Re-render profile
            this.render();
            
            // Show success message
            this.showMessage('Profile updated successfully!', 'success');
            
        } catch (error) {
            console.error('❌ UserProfile: Failed to update profile:', error);
            this.showMessage('Failed to update profile. Please try again.', 'error');
        }
    }

    /**
     * Handle settings changes
     */
    async handleSettingsChange(input) {
        try {
            const updates = {};
            
            if (input.name === 'privacy') {
                updates['preferences.privacy'] = input.value;
            } else if (input.id === 'notifications') {
                updates['preferences.notifications'] = input.checked;
            } else if (input.id === 'email-updates') {
                updates['preferences.emailUpdates'] = input.checked;
            }
            
            await AuthService.updateUserProfile(updates);
            
            console.log('✅ UserProfile: Settings updated');
            
        } catch (error) {
            console.error('❌ UserProfile: Failed to update settings:', error);
        }
    }

    /**
     * Utility methods
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    formatDate(timestamp) {
        if (!timestamp) return 'Unknown';
        
        const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
        return date.toLocaleDateString('en-US', { 
            year: 'numeric', 
            month: 'long' 
        });
    }

    showMessage(message, type) {
        // Create and show toast notification
        const toast = document.createElement('div');
        toast.className = `alert alert-${type === 'error' ? 'danger' : 'success'} alert-dismissible fade show position-fixed`;
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999;';
        toast.innerHTML = `${message}<button type="button" class="btn-close" data-bs-dismiss="alert"></button>`;
        
        document.body.appendChild(toast);
        setTimeout(() => toast.remove(), 5000);
    }

    renderError(message) {
        if (this.container) {
            this.container.innerHTML = `
                <div class="error-state">
                    <i class="fas fa-exclamation-triangle"></i>
                    <h3>Error Loading Profile</h3>
                    <p>${message}</p>
                    <button class="btn btn-danger" onclick="location.reload()">Try Again</button>
                </div>
            `;
        }
    }
}

export default UserProfile;
