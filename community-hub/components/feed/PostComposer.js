/**
 * Post Composer Component
 * Handles post creation with text, images, and other media
 */

import AuthService from '../../services/AuthService.js';
import PostService from '../../services/PostService.js';

class PostComposer {
    constructor(container) {
        this.container = container;
        this.element = null;
        this.selectedImages = [];
        this.isSubmitting = false;
        this.onPostCreatedCallback = null;
        
        this.init();
    }

    /**
     * Initialize the post composer
     */
    init() {
        this.render();
        this.setupEventListeners();
        this.updateVisibility();
    }

    /**
     * Render the post composer
     */
    render() {
        const currentUser = AuthService.getCurrentUser();
        
        this.element = document.createElement('div');
        this.element.className = 'post-composer';
        
        this.element.innerHTML = `
            <div class="composer-header">
                <img src="${currentUser?.photoURL || '/images/time.jpeg'}" 
                     alt="${currentUser?.displayName || 'User'}" 
                     class="composer-avatar">
                <div class="composer-user-info">
                    <h5 class="composer-username">${currentUser?.displayName || 'Anonymous User'}</h5>
                    <select class="form-select form-select-sm composer-visibility">
                        <option value="public">🌍 Public</option>
                        <option value="followers">👥 Followers only</option>
                    </select>
                </div>
            </div>
            
            <div class="composer-content">
                <textarea class="form-control composer-textarea" 
                          placeholder="What's happening in the barber world today?"
                          rows="3"
                          maxlength="2000"></textarea>
                
                <div class="composer-char-count">
                    <span class="char-count">0</span>/2000
                </div>
                
                <div class="composer-images" style="display: none;">
                    <div class="images-preview"></div>
                </div>
                
                <div class="composer-tags">
                    <input type="text" class="form-control form-control-sm tags-input" 
                           placeholder="Add tags (separated by commas)">
                    <div class="tags-preview"></div>
                </div>
            </div>
            
            <div class="composer-actions">
                <div class="composer-tools">
                    <button type="button" class="btn btn-sm btn-outline-secondary tool-btn" 
                            data-tool="image" title="Add Images">
                        <i class="fas fa-image"></i>
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-secondary tool-btn" 
                            data-tool="emoji" title="Add Emoji">
                        <i class="fas fa-smile"></i>
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-secondary tool-btn" 
                            data-tool="location" title="Add Location">
                        <i class="fas fa-map-marker-alt"></i>
                    </button>
                </div>
                
                <div class="composer-submit">
                    <button type="button" class="btn btn-danger submit-btn" disabled>
                        <i class="fas fa-paper-plane me-2"></i>Post
                    </button>
                </div>
            </div>
            
            <input type="file" class="d-none image-input" 
                   accept="image/*" multiple max="5">
        `;
        
        this.container.appendChild(this.element);
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        if (!this.element) return;
        
        const textarea = this.element.querySelector('.composer-textarea');
        const submitBtn = this.element.querySelector('.submit-btn');
        const imageInput = this.element.querySelector('.image-input');
        const tagsInput = this.element.querySelector('.tags-input');
        
        // Textarea events
        textarea.addEventListener('input', () => {
            this.updateCharCount();
            this.updateSubmitButton();
        });
        
        textarea.addEventListener('paste', (e) => {
            this.handlePaste(e);
        });
        
        // Submit button
        submitBtn.addEventListener('click', () => {
            this.submitPost();
        });
        
        // Tool buttons
        this.element.addEventListener('click', (e) => {
            const tool = e.target.closest('[data-tool]')?.dataset.tool;
            if (tool) {
                this.handleToolClick(tool);
            }
        });
        
        // Image input
        imageInput.addEventListener('change', (e) => {
            this.handleImageSelection(e.target.files);
        });
        
        // Tags input
        tagsInput.addEventListener('input', () => {
            this.updateTagsPreview();
        });
        
        // Keyboard shortcuts
        textarea.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 'Enter') {
                this.submitPost();
            }
        });
        
        // Auth state changes
        window.addEventListener('authStateChanged', (e) => {
            this.updateVisibility();
        });
    }

    /**
     * Handle tool button clicks
     */
    handleToolClick(tool) {
        switch (tool) {
            case 'image':
                this.openImageSelector();
                break;
            case 'emoji':
                this.openEmojiPicker();
                break;
            case 'location':
                this.addLocation();
                break;
        }
    }

    /**
     * Open image selector
     */
    openImageSelector() {
        const imageInput = this.element.querySelector('.image-input');
        imageInput.click();
    }

    /**
     * Handle image selection
     */
    handleImageSelection(files) {
        const maxImages = 5;
        const maxSize = 5 * 1024 * 1024; // 5MB
        
        Array.from(files).forEach(file => {
            if (this.selectedImages.length >= maxImages) {
                this.showError(`Maximum ${maxImages} images allowed`);
                return;
            }
            
            if (file.size > maxSize) {
                this.showError(`Image ${file.name} is too large (max 5MB)`);
                return;
            }
            
            if (!file.type.startsWith('image/')) {
                this.showError(`${file.name} is not a valid image`);
                return;
            }
            
            this.addImage(file);
        });
        
        this.updateImagesPreview();
        this.updateSubmitButton();
    }

    /**
     * Add image to selection
     */
    addImage(file) {
        const reader = new FileReader();
        reader.onload = (e) => {
            const imageData = {
                file: file,
                url: e.target.result,
                name: file.name,
                size: file.size
            };
            
            this.selectedImages.push(imageData);
            this.updateImagesPreview();
        };
        reader.readAsDataURL(file);
    }

    /**
     * Update images preview
     */
    updateImagesPreview() {
        const imagesContainer = this.element.querySelector('.composer-images');
        const imagesPreview = this.element.querySelector('.images-preview');
        
        if (this.selectedImages.length === 0) {
            imagesContainer.style.display = 'none';
            return;
        }
        
        imagesContainer.style.display = 'block';
        
        imagesPreview.innerHTML = this.selectedImages.map((image, index) => `
            <div class="image-preview-item" data-index="${index}">
                <img src="${image.url}" alt="${image.name}" class="preview-image">
                <button type="button" class="btn btn-sm btn-danger remove-image-btn" 
                        data-index="${index}" title="Remove image">
                    <i class="fas fa-times"></i>
                </button>
                <div class="image-info">
                    <small>${image.name}</small>
                </div>
            </div>
        `).join('');
        
        // Add remove image listeners
        imagesPreview.addEventListener('click', (e) => {
            const removeBtn = e.target.closest('.remove-image-btn');
            if (removeBtn) {
                const index = parseInt(removeBtn.dataset.index);
                this.removeImage(index);
            }
        });
    }

    /**
     * Remove image from selection
     */
    removeImage(index) {
        this.selectedImages.splice(index, 1);
        this.updateImagesPreview();
        this.updateSubmitButton();
    }

    /**
     * Update character count
     */
    updateCharCount() {
        const textarea = this.element.querySelector('.composer-textarea');
        const charCount = this.element.querySelector('.char-count');
        const count = textarea.value.length;
        
        charCount.textContent = count;
        charCount.className = count > 1800 ? 'char-count warning' : 'char-count';
    }

    /**
     * Update tags preview
     */
    updateTagsPreview() {
        const tagsInput = this.element.querySelector('.tags-input');
        const tagsPreview = this.element.querySelector('.tags-preview');
        
        const tags = tagsInput.value
            .split(',')
            .map(tag => tag.trim())
            .filter(tag => tag.length > 0)
            .slice(0, 10); // Max 10 tags
        
        if (tags.length === 0) {
            tagsPreview.innerHTML = '';
            return;
        }
        
        tagsPreview.innerHTML = tags.map(tag => `
            <span class="tag-preview">#${tag}</span>
        `).join('');
    }

    /**
     * Update submit button state
     */
    updateSubmitButton() {
        const textarea = this.element.querySelector('.composer-textarea');
        const submitBtn = this.element.querySelector('.submit-btn');
        
        const hasContent = textarea.value.trim().length > 0;
        const hasImages = this.selectedImages.length > 0;
        const isValid = hasContent || hasImages;
        
        submitBtn.disabled = !isValid || this.isSubmitting;
    }

    /**
     * Submit post
     */
    async submitPost() {
        if (this.isSubmitting) return;
        
        const textarea = this.element.querySelector('.composer-textarea');
        const tagsInput = this.element.querySelector('.tags-input');
        const visibilitySelect = this.element.querySelector('.composer-visibility');
        
        const content = textarea.value.trim();
        const tags = tagsInput.value
            .split(',')
            .map(tag => tag.trim())
            .filter(tag => tag.length > 0);
        
        if (!content && this.selectedImages.length === 0) {
            this.showError('Please add some content or images');
            return;
        }
        
        try {
            this.isSubmitting = true;
            this.showSubmittingState();
            
            const postData = {
                content,
                tags,
                visibility: visibilitySelect.value,
                images: this.selectedImages
            };
            
            console.log('📝 PostComposer: Creating post...', postData);
            
            const post = await PostService.createPost(postData);
            
            console.log('✅ PostComposer: Post created successfully', post);
            
            // Reset form
            this.resetForm();
            
            // Notify parent component
            if (this.onPostCreatedCallback) {
                this.onPostCreatedCallback(post);
            }
            
            this.showSuccess('Post created successfully!');
            
        } catch (error) {
            console.error('❌ PostComposer: Failed to create post:', error);
            this.showError('Failed to create post. Please try again.');
        } finally {
            this.isSubmitting = false;
            this.hideSubmittingState();
        }
    }

    /**
     * Reset form after successful submission
     */
    resetForm() {
        const textarea = this.element.querySelector('.composer-textarea');
        const tagsInput = this.element.querySelector('.tags-input');
        const imageInput = this.element.querySelector('.image-input');
        
        textarea.value = '';
        tagsInput.value = '';
        imageInput.value = '';
        
        this.selectedImages = [];
        this.updateImagesPreview();
        this.updateTagsPreview();
        this.updateCharCount();
        this.updateSubmitButton();
    }

    /**
     * Show submitting state
     */
    showSubmittingState() {
        const submitBtn = this.element.querySelector('.submit-btn');
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Posting...';
        submitBtn.disabled = true;
    }

    /**
     * Hide submitting state
     */
    hideSubmittingState() {
        const submitBtn = this.element.querySelector('.submit-btn');
        submitBtn.innerHTML = '<i class="fas fa-paper-plane me-2"></i>Post';
        this.updateSubmitButton();
    }

    /**
     * Update visibility based on auth state
     */
    updateVisibility() {
        const currentUser = AuthService.getCurrentUser();
        
        if (currentUser) {
            this.element.style.display = 'block';
        } else {
            this.element.style.display = 'none';
        }
    }

    /**
     * Handle paste events (for images)
     */
    handlePaste(e) {
        const items = e.clipboardData?.items;
        if (!items) return;
        
        for (let item of items) {
            if (item.type.startsWith('image/')) {
                e.preventDefault();
                const file = item.getAsFile();
                if (file) {
                    this.handleImageSelection([file]);
                }
                break;
            }
        }
    }

    /**
     * Focus the composer
     */
    focus() {
        const textarea = this.element.querySelector('.composer-textarea');
        if (textarea) {
            textarea.focus();
        }
    }

    /**
     * Show the composer
     */
    show() {
        if (this.element) {
            this.element.style.display = 'block';
        }
    }

    /**
     * Hide the composer
     */
    hide() {
        if (this.element) {
            this.element.style.display = 'none';
        }
    }

    /**
     * Set callback for post creation
     */
    onPostCreated(callback) {
        this.onPostCreatedCallback = callback;
    }

    /**
     * Show error message
     */
    showError(message) {
        const toast = document.createElement('div');
        toast.className = 'alert alert-danger alert-dismissible fade show position-fixed';
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999;';
        toast.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(toast);
        setTimeout(() => toast.remove(), 5000);
    }

    /**
     * Show success message
     */
    showSuccess(message) {
        const toast = document.createElement('div');
        toast.className = 'alert alert-success alert-dismissible fade show position-fixed';
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999;';
        toast.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(toast);
        setTimeout(() => toast.remove(), 3000);
    }

    /**
     * Cleanup
     */
    destroy() {
        if (this.element && this.element.parentNode) {
            this.element.parentNode.removeChild(this.element);
        }
    }
}

export default PostComposer;
