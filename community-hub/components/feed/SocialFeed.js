/**
 * Social Feed Component
 * Main feed component that displays posts and handles user interactions
 */

import PostService from '../../services/PostService.js';
import AuthService from '../../services/AuthService.js';
import PostCard from './PostCard.js';
import PostComposer from './PostComposer.js';

class SocialFeed {
    constructor(containerId = 'social-feed') {
        this.containerId = containerId;
        this.container = null;
        this.posts = [];
        this.isLoading = false;
        this.hasMorePosts = true;
        this.lastPost = null;
        this.feedUnsubscribe = null;
        this.postComposer = null;
        
        this.init();
    }

    /**
     * Initialize the social feed
     */
    async init() {
        try {
            console.log('📱 SocialFeed: Initializing...');
            
            // Find or create container
            this.container = document.getElementById(this.containerId);
            if (!this.container) {
                this.container = document.createElement('div');
                this.container.id = this.containerId;
                this.container.className = 'social-feed-container';
                document.body.appendChild(this.container);
            }
            
            // Render initial structure
            this.renderFeedStructure();
            
            // Initialize post composer
            this.initializePostComposer();
            
            // Setup event listeners
            this.setupEventListeners();
            
            // Load initial posts
            await this.loadPosts();
            
            // Setup real-time updates
            this.setupRealTimeUpdates();
            
            console.log('✅ SocialFeed: Initialized successfully');
            
        } catch (error) {
            console.error('❌ SocialFeed: Initialization failed:', error);
            this.renderError('Failed to load social feed');
        }
    }

    /**
     * Render the feed structure
     */
    renderFeedStructure() {
        this.container.innerHTML = `
            <div class="feed-header">
                <h2 class="feed-title">
                    <i class="fas fa-users me-2"></i>
                    Community Feed
                </h2>
                <div class="feed-controls">
                    <button class="btn btn-sm btn-outline-danger refresh-btn" title="Refresh Feed">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                </div>
            </div>
            
            <div class="post-composer-container">
                <!-- Post composer will be inserted here -->
            </div>
            
            <div class="feed-filters">
                <button class="filter-btn active" data-filter="all">
                    <i class="fas fa-globe me-1"></i>All Posts
                </button>
                <button class="filter-btn" data-filter="following">
                    <i class="fas fa-user-friends me-1"></i>Following
                </button>
                <button class="filter-btn" data-filter="trending">
                    <i class="fas fa-fire me-1"></i>Trending
                </button>
            </div>
            
            <div class="posts-container">
                <div class="loading-posts">
                    <div class="spinner-border text-danger" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p>Loading posts...</p>
                </div>
            </div>
            
            <div class="load-more-container">
                <button class="btn btn-outline-danger load-more-btn" style="display: none;">
                    <i class="fas fa-chevron-down me-2"></i>Load More Posts
                </button>
            </div>
            
            <div class="feed-empty" style="display: none;">
                <div class="empty-state">
                    <i class="fas fa-comments"></i>
                    <h3>No posts yet</h3>
                    <p>Be the first to share something with the community!</p>
                    <button class="btn btn-danger create-post-btn">
                        <i class="fas fa-plus me-2"></i>Create Post
                    </button>
                </div>
            </div>
        `;
    }

    /**
     * Initialize post composer
     */
    initializePostComposer() {
        const composerContainer = this.container.querySelector('.post-composer-container');
        if (composerContainer) {
            this.postComposer = new PostComposer(composerContainer);
            
            // Listen for new posts
            this.postComposer.onPostCreated((post) => {
                this.handleNewPost(post);
            });
        }
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Refresh button
        this.container.addEventListener('click', (e) => {
            if (e.target.matches('.refresh-btn, .refresh-btn *')) {
                e.preventDefault();
                this.refreshFeed();
            }
            
            if (e.target.matches('.load-more-btn')) {
                e.preventDefault();
                this.loadMorePosts();
            }
            
            if (e.target.matches('.create-post-btn')) {
                e.preventDefault();
                this.focusPostComposer();
            }
            
            if (e.target.matches('.filter-btn')) {
                e.preventDefault();
                this.handleFilterChange(e.target);
            }
        });
        
        // Infinite scroll
        this.setupInfiniteScroll();
        
        // Auth state changes
        window.addEventListener('authStateChanged', (e) => {
            this.handleAuthStateChange(e.detail.user);
        });
    }

    /**
     * Setup infinite scroll
     */
    setupInfiniteScroll() {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting && this.hasMorePosts && !this.isLoading) {
                    this.loadMorePosts();
                }
            });
        }, {
            rootMargin: '100px'
        });

        const loadMoreContainer = this.container.querySelector('.load-more-container');
        if (loadMoreContainer) {
            observer.observe(loadMoreContainer);
        }
    }

    /**
     * Load initial posts
     */
    async loadPosts() {
        if (this.isLoading) return;
        
        try {
            this.isLoading = true;
            this.showLoadingState();
            
            console.log('📖 SocialFeed: Loading posts...');
            
            const posts = await PostService.getFeedPosts({
                limit: 10
            });
            
            this.posts = posts;
            this.lastPost = posts.length > 0 ? posts[posts.length - 1] : null;
            this.hasMorePosts = posts.length === 10;
            
            this.renderPosts();
            this.hideLoadingState();
            
            console.log(`✅ SocialFeed: Loaded ${posts.length} posts`);
            
        } catch (error) {
            console.error('❌ SocialFeed: Failed to load posts:', error);
            this.renderError('Failed to load posts');
        } finally {
            this.isLoading = false;
        }
    }

    /**
     * Load more posts (pagination)
     */
    async loadMorePosts() {
        if (this.isLoading || !this.hasMorePosts) return;
        
        try {
            this.isLoading = true;
            
            const morePosts = await PostService.getFeedPosts({
                limit: 10,
                lastPost: this.lastPost
            });
            
            this.posts.push(...morePosts);
            this.lastPost = morePosts.length > 0 ? morePosts[morePosts.length - 1] : this.lastPost;
            this.hasMorePosts = morePosts.length === 10;
            
            this.renderNewPosts(morePosts);
            
            console.log(`✅ SocialFeed: Loaded ${morePosts.length} more posts`);
            
        } catch (error) {
            console.error('❌ SocialFeed: Failed to load more posts:', error);
        } finally {
            this.isLoading = false;
        }
    }

    /**
     * Refresh the entire feed
     */
    async refreshFeed() {
        try {
            console.log('🔄 SocialFeed: Refreshing feed...');
            
            // Reset state
            this.posts = [];
            this.lastPost = null;
            this.hasMorePosts = true;
            
            // Reload posts
            await this.loadPosts();
            
            // Show success message
            this.showMessage('Feed refreshed!', 'success');
            
        } catch (error) {
            console.error('❌ SocialFeed: Failed to refresh feed:', error);
            this.showMessage('Failed to refresh feed', 'error');
        }
    }

    /**
     * Render posts in the feed
     */
    renderPosts() {
        const postsContainer = this.container.querySelector('.posts-container');
        const emptyState = this.container.querySelector('.feed-empty');
        
        if (this.posts.length === 0) {
            postsContainer.innerHTML = '';
            emptyState.style.display = 'block';
            return;
        }
        
        emptyState.style.display = 'none';
        
        // Clear container
        postsContainer.innerHTML = '';
        
        // Render each post
        this.posts.forEach(post => {
            const postCard = new PostCard(post);
            postsContainer.appendChild(postCard.render());
        });
        
        // Update load more button
        this.updateLoadMoreButton();
    }

    /**
     * Render new posts (for pagination)
     */
    renderNewPosts(newPosts) {
        const postsContainer = this.container.querySelector('.posts-container');
        
        newPosts.forEach(post => {
            const postCard = new PostCard(post);
            postsContainer.appendChild(postCard.render());
        });
        
        this.updateLoadMoreButton();
    }

    /**
     * Handle new post creation
     */
    handleNewPost(post) {
        console.log('✨ SocialFeed: New post created:', post.id);
        
        // Add to beginning of posts array
        this.posts.unshift(post);
        
        // Re-render posts
        this.renderPosts();
        
        // Show success message
        this.showMessage('Post created successfully!', 'success');
        
        // Scroll to top
        this.container.scrollIntoView({ behavior: 'smooth' });
    }

    /**
     * Handle filter changes
     */
    handleFilterChange(filterBtn) {
        // Update active filter
        this.container.querySelectorAll('.filter-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        filterBtn.classList.add('active');
        
        const filter = filterBtn.dataset.filter;
        console.log('🔍 SocialFeed: Filter changed to:', filter);
        
        // TODO: Implement different filter logic
        // For now, just refresh the feed
        this.refreshFeed();
    }

    /**
     * Handle authentication state changes
     */
    handleAuthStateChange(user) {
        if (user) {
            // User signed in - show post composer
            if (this.postComposer) {
                this.postComposer.show();
            }
        } else {
            // User signed out - hide post composer
            if (this.postComposer) {
                this.postComposer.hide();
            }
        }
    }

    /**
     * Setup real-time updates
     */
    setupRealTimeUpdates() {
        // Listen for real-time post updates
        this.feedUnsubscribe = PostService.listenToFeedUpdates((posts) => {
            // Check for new posts
            const newPosts = posts.filter(post => 
                !this.posts.find(existingPost => existingPost.id === post.id)
            );
            
            if (newPosts.length > 0) {
                console.log(`🔄 SocialFeed: ${newPosts.length} new posts received`);
                
                // Add new posts to the beginning
                this.posts.unshift(...newPosts);
                
                // Re-render if user is at the top
                if (window.scrollY < 100) {
                    this.renderPosts();
                } else {
                    // Show notification about new posts
                    this.showNewPostsNotification(newPosts.length);
                }
            }
        });
    }

    /**
     * Show notification about new posts
     */
    showNewPostsNotification(count) {
        const notification = document.createElement('div');
        notification.className = 'new-posts-notification';
        notification.innerHTML = `
            <i class="fas fa-arrow-up me-2"></i>
            ${count} new post${count > 1 ? 's' : ''} available
            <button class="btn btn-sm btn-danger ms-2">View</button>
        `;
        
        notification.addEventListener('click', () => {
            this.renderPosts();
            window.scrollTo({ top: 0, behavior: 'smooth' });
            notification.remove();
        });
        
        this.container.insertBefore(notification, this.container.firstChild);
        
        // Auto-remove after 10 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 10000);
    }

    /**
     * Focus post composer
     */
    focusPostComposer() {
        if (this.postComposer) {
            this.postComposer.focus();
        }
    }

    /**
     * Update load more button visibility
     */
    updateLoadMoreButton() {
        const loadMoreBtn = this.container.querySelector('.load-more-btn');
        if (loadMoreBtn) {
            loadMoreBtn.style.display = this.hasMorePosts ? 'block' : 'none';
        }
    }

    /**
     * Show loading state
     */
    showLoadingState() {
        const loadingElement = this.container.querySelector('.loading-posts');
        if (loadingElement) {
            loadingElement.style.display = 'block';
        }
    }

    /**
     * Hide loading state
     */
    hideLoadingState() {
        const loadingElement = this.container.querySelector('.loading-posts');
        if (loadingElement) {
            loadingElement.style.display = 'none';
        }
    }

    /**
     * Show message to user
     */
    showMessage(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `alert alert-${type === 'error' ? 'danger' : 'success'} alert-dismissible fade show position-fixed`;
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999;';
        toast.innerHTML = `${message}<button type="button" class="btn-close" data-bs-dismiss="alert"></button>`;
        
        document.body.appendChild(toast);
        setTimeout(() => toast.remove(), 5000);
    }

    /**
     * Render error state
     */
    renderError(message) {
        const postsContainer = this.container.querySelector('.posts-container');
        postsContainer.innerHTML = `
            <div class="error-state">
                <i class="fas fa-exclamation-triangle"></i>
                <h3>Error</h3>
                <p>${message}</p>
                <button class="btn btn-danger" onclick="location.reload()">Try Again</button>
            </div>
        `;
    }

    /**
     * Cleanup
     */
    destroy() {
        if (this.feedUnsubscribe) {
            this.feedUnsubscribe();
        }
        
        if (this.postComposer) {
            this.postComposer.destroy();
        }
    }
}

export default SocialFeed;
