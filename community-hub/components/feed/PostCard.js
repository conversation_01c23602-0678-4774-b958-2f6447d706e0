/**
 * Post Card Component
 * Displays individual posts in the social feed
 */

import AuthService from '../../services/AuthService.js';
import PostService from '../../services/PostService.js';

class PostCard {
    constructor(post) {
        this.post = post;
        this.element = null;
        this.isLiked = false;
        this.likeCount = post.likes || 0;
        this.commentCount = post.commentCount || 0;
        
        this.checkIfLiked();
    }

    /**
     * Check if current user has liked this post
     */
    async checkIfLiked() {
        const currentUser = AuthService.getCurrentUser();
        if (currentUser && this.post.likes) {
            this.isLiked = this.post.likes.includes(currentUser.uid);
        }
    }

    /**
     * Render the post card
     */
    render() {
        this.element = document.createElement('article');
        this.element.className = 'post-card';
        this.element.dataset.postId = this.post.id;
        
        this.element.innerHTML = `
            <div class="post-header">
                <div class="post-author">
                    <img src="${this.post.author?.photoURL || '/images/time.jpeg'}" 
                         alt="${this.post.author?.displayName || 'User'}" 
                         class="author-avatar">
                    <div class="author-info">
                        <h4 class="author-name">
                            ${this.post.author?.displayName || 'Anonymous User'}
                            ${this.post.author?.verified ? '<i class="fas fa-check-circle verified-badge" title="Verified Barber"></i>' : ''}
                        </h4>
                        <p class="post-time">
                            <i class="fas fa-clock me-1"></i>
                            ${this.formatTime(this.post.createdAt)}
                        </p>
                    </div>
                </div>
                <div class="post-menu">
                    <button class="btn btn-sm btn-ghost post-menu-btn" data-bs-toggle="dropdown">
                        <i class="fas fa-ellipsis-h"></i>
                    </button>
                    <ul class="dropdown-menu">
                        ${this.renderPostMenuItems()}
                    </ul>
                </div>
            </div>
            
            <div class="post-content">
                <p class="post-text">${this.formatPostContent(this.post.content)}</p>
                ${this.renderPostImages()}
                ${this.renderPostTags()}
            </div>
            
            <div class="post-actions">
                <button class="action-btn like-btn ${this.isLiked ? 'liked' : ''}" 
                        data-action="like" title="Like">
                    <i class="fas fa-heart"></i>
                    <span class="action-count">${this.likeCount}</span>
                </button>
                
                <button class="action-btn comment-btn" 
                        data-action="comment" title="Comment">
                    <i class="fas fa-comment"></i>
                    <span class="action-count">${this.commentCount}</span>
                </button>
                
                <button class="action-btn share-btn" 
                        data-action="share" title="Share">
                    <i class="fas fa-share"></i>
                </button>
                
                <button class="action-btn bookmark-btn" 
                        data-action="bookmark" title="Bookmark">
                    <i class="fas fa-bookmark"></i>
                </button>
            </div>
            
            <div class="post-comments" style="display: none;">
                <div class="comments-container">
                    <!-- Comments will be loaded here -->
                </div>
                <div class="comment-form">
                    <div class="comment-input-group">
                        <img src="${AuthService.getCurrentUser()?.photoURL || '/images/time.jpeg'}" 
                             alt="Your avatar" class="comment-avatar">
                        <input type="text" class="form-control comment-input" 
                               placeholder="Write a comment...">
                        <button class="btn btn-danger btn-sm comment-submit">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        this.setupEventListeners();
        return this.element;
    }

    /**
     * Render post menu items
     */
    renderPostMenuItems() {
        const currentUser = AuthService.getCurrentUser();
        const isOwner = currentUser && currentUser.uid === this.post.authorId;
        
        let menuItems = `
            <li><a class="dropdown-item" href="#" data-action="copy-link">
                <i class="fas fa-link me-2"></i>Copy Link
            </a></li>
        `;
        
        if (isOwner) {
            menuItems += `
                <li><a class="dropdown-item" href="#" data-action="edit">
                    <i class="fas fa-edit me-2"></i>Edit Post
                </a></li>
                <li><hr class="dropdown-divider"></li>
                <li><a class="dropdown-item text-danger" href="#" data-action="delete">
                    <i class="fas fa-trash me-2"></i>Delete Post
                </a></li>
            `;
        } else {
            menuItems += `
                <li><hr class="dropdown-divider"></li>
                <li><a class="dropdown-item text-warning" href="#" data-action="report">
                    <i class="fas fa-flag me-2"></i>Report Post
                </a></li>
            `;
        }
        
        return menuItems;
    }

    /**
     * Render post images
     */
    renderPostImages() {
        if (!this.post.images || this.post.images.length === 0) {
            return '';
        }
        
        return `
            <div class="post-images">
                ${this.post.images.map(image => `
                    <img src="${image.url}" alt="Post image" class="post-image" 
                         onclick="this.openImageModal('${image.url}')">
                `).join('')}
            </div>
        `;
    }

    /**
     * Render post tags
     */
    renderPostTags() {
        if (!this.post.tags || this.post.tags.length === 0) {
            return '';
        }
        
        return `
            <div class="post-tags">
                ${this.post.tags.map(tag => `
                    <span class="post-tag">#${tag}</span>
                `).join('')}
            </div>
        `;
    }

    /**
     * Format post content (handle mentions, hashtags, etc.)
     */
    formatPostContent(content) {
        if (!content) return '';
        
        // Convert URLs to links
        content = content.replace(
            /(https?:\/\/[^\s]+)/g,
            '<a href="$1" target="_blank" rel="noopener noreferrer">$1</a>'
        );
        
        // Convert hashtags to clickable links
        content = content.replace(
            /#(\w+)/g,
            '<a href="#" class="hashtag" data-tag="$1">#$1</a>'
        );
        
        // Convert mentions to clickable links
        content = content.replace(
            /@(\w+)/g,
            '<a href="#" class="mention" data-user="$1">@$1</a>'
        );
        
        return content;
    }

    /**
     * Format time display
     */
    formatTime(timestamp) {
        if (!timestamp) return 'Unknown time';
        
        const now = new Date();
        const postTime = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
        const diffMs = now - postTime;
        const diffMins = Math.floor(diffMs / 60000);
        const diffHours = Math.floor(diffMs / 3600000);
        const diffDays = Math.floor(diffMs / 86400000);
        
        if (diffMins < 1) return 'Just now';
        if (diffMins < 60) return `${diffMins}m ago`;
        if (diffHours < 24) return `${diffHours}h ago`;
        if (diffDays < 7) return `${diffDays}d ago`;
        
        return postTime.toLocaleDateString();
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        if (!this.element) return;
        
        // Action buttons
        this.element.addEventListener('click', (e) => {
            const action = e.target.closest('[data-action]')?.dataset.action;
            if (action) {
                e.preventDefault();
                this.handleAction(action, e.target);
            }
        });
        
        // Comment form
        const commentInput = this.element.querySelector('.comment-input');
        const commentSubmit = this.element.querySelector('.comment-submit');
        
        if (commentInput && commentSubmit) {
            commentInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    this.submitComment();
                }
            });
            
            commentSubmit.addEventListener('click', () => {
                this.submitComment();
            });
        }
    }

    /**
     * Handle action button clicks
     */
    async handleAction(action, target) {
        const currentUser = AuthService.getCurrentUser();
        
        if (!currentUser && ['like', 'comment', 'bookmark'].includes(action)) {
            this.showAuthRequired();
            return;
        }
        
        try {
            switch (action) {
                case 'like':
                    await this.toggleLike();
                    break;
                case 'comment':
                    this.toggleComments();
                    break;
                case 'share':
                    this.sharePost();
                    break;
                case 'bookmark':
                    await this.toggleBookmark();
                    break;
                case 'copy-link':
                    this.copyPostLink();
                    break;
                case 'edit':
                    this.editPost();
                    break;
                case 'delete':
                    await this.deletePost();
                    break;
                case 'report':
                    this.reportPost();
                    break;
            }
        } catch (error) {
            console.error(`Failed to ${action} post:`, error);
            this.showError(`Failed to ${action} post`);
        }
    }

    /**
     * Toggle like status
     */
    async toggleLike() {
        const likeBtn = this.element.querySelector('.like-btn');
        const likeCount = this.element.querySelector('.like-btn .action-count');
        
        // Optimistic update
        this.isLiked = !this.isLiked;
        this.likeCount += this.isLiked ? 1 : -1;
        
        likeBtn.classList.toggle('liked', this.isLiked);
        likeCount.textContent = this.likeCount;
        
        try {
            await PostService.toggleLike(this.post.id);
        } catch (error) {
            // Revert on error
            this.isLiked = !this.isLiked;
            this.likeCount += this.isLiked ? 1 : -1;
            likeBtn.classList.toggle('liked', this.isLiked);
            likeCount.textContent = this.likeCount;
            throw error;
        }
    }

    /**
     * Toggle comments visibility
     */
    toggleComments() {
        const commentsSection = this.element.querySelector('.post-comments');
        const isVisible = commentsSection.style.display !== 'none';
        
        if (isVisible) {
            commentsSection.style.display = 'none';
        } else {
            commentsSection.style.display = 'block';
            this.loadComments();
            
            // Focus comment input
            const commentInput = commentsSection.querySelector('.comment-input');
            if (commentInput) {
                setTimeout(() => commentInput.focus(), 100);
            }
        }
    }

    /**
     * Load comments for this post
     */
    async loadComments() {
        const commentsContainer = this.element.querySelector('.comments-container');
        commentsContainer.innerHTML = '<div class="loading-comments">Loading comments...</div>';
        
        try {
            const comments = await PostService.getPostComments(this.post.id);
            this.renderComments(comments);
        } catch (error) {
            commentsContainer.innerHTML = '<div class="error-comments">Failed to load comments</div>';
        }
    }

    /**
     * Render comments
     */
    renderComments(comments) {
        const commentsContainer = this.element.querySelector('.comments-container');
        
        if (comments.length === 0) {
            commentsContainer.innerHTML = '<div class="no-comments">No comments yet</div>';
            return;
        }
        
        commentsContainer.innerHTML = comments.map(comment => `
            <div class="comment-item" data-comment-id="${comment.id}">
                <img src="${comment.author?.photoURL || '/images/time.jpeg'}" 
                     alt="${comment.author?.displayName}" class="comment-avatar">
                <div class="comment-content">
                    <div class="comment-header">
                        <span class="comment-author">${comment.author?.displayName || 'Anonymous'}</span>
                        <span class="comment-time">${this.formatTime(comment.createdAt)}</span>
                    </div>
                    <p class="comment-text">${comment.content}</p>
                </div>
            </div>
        `).join('');
    }

    /**
     * Submit new comment
     */
    async submitComment() {
        const commentInput = this.element.querySelector('.comment-input');
        const content = commentInput.value.trim();
        
        if (!content) return;
        
        try {
            commentInput.disabled = true;
            
            const comment = await PostService.createComment(this.post.id, content);
            
            // Add comment to UI
            this.addCommentToUI(comment);
            
            // Update comment count
            this.commentCount++;
            this.element.querySelector('.comment-btn .action-count').textContent = this.commentCount;
            
            // Clear input
            commentInput.value = '';
            
        } catch (error) {
            this.showError('Failed to post comment');
        } finally {
            commentInput.disabled = false;
        }
    }

    /**
     * Add comment to UI
     */
    addCommentToUI(comment) {
        const commentsContainer = this.element.querySelector('.comments-container');
        const noComments = commentsContainer.querySelector('.no-comments');
        
        if (noComments) {
            noComments.remove();
        }
        
        const commentElement = document.createElement('div');
        commentElement.className = 'comment-item';
        commentElement.innerHTML = `
            <img src="${comment.author?.photoURL || '/images/time.jpeg'}" 
                 alt="${comment.author?.displayName}" class="comment-avatar">
            <div class="comment-content">
                <div class="comment-header">
                    <span class="comment-author">${comment.author?.displayName || 'Anonymous'}</span>
                    <span class="comment-time">Just now</span>
                </div>
                <p class="comment-text">${comment.content}</p>
            </div>
        `;
        
        commentsContainer.appendChild(commentElement);
    }

    /**
     * Show authentication required message
     */
    showAuthRequired() {
        const toast = document.createElement('div');
        toast.className = 'alert alert-warning alert-dismissible fade show position-fixed';
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999;';
        toast.innerHTML = `
            Please sign in to interact with posts
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(toast);
        setTimeout(() => toast.remove(), 5000);
    }

    /**
     * Show error message
     */
    showError(message) {
        const toast = document.createElement('div');
        toast.className = 'alert alert-danger alert-dismissible fade show position-fixed';
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999;';
        toast.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(toast);
        setTimeout(() => toast.remove(), 5000);
    }

    /**
     * Share post
     */
    sharePost() {
        if (navigator.share) {
            navigator.share({
                title: `Post by ${this.post.author?.displayName}`,
                text: this.post.content,
                url: window.location.href
            });
        } else {
            this.copyPostLink();
        }
    }

    /**
     * Copy post link
     */
    copyPostLink() {
        const url = `${window.location.origin}${window.location.pathname}#post-${this.post.id}`;
        navigator.clipboard.writeText(url).then(() => {
            this.showSuccess('Link copied to clipboard!');
        });
    }

    /**
     * Show success message
     */
    showSuccess(message) {
        const toast = document.createElement('div');
        toast.className = 'alert alert-success alert-dismissible fade show position-fixed';
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999;';
        toast.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(toast);
        setTimeout(() => toast.remove(), 3000);
    }
}

export default PostCard;
