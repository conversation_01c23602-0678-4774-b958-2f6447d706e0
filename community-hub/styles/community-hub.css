/**
 * Barber Brothers Community Hub Styles
 * Modern, responsive design with dark theme and red accents
 */

/* ===== VARIABLES ===== */
:root {
    --primary-color: #dc3545;
    --primary-dark: #c82333;
    --background-dark: #1a1a1a;
    --background-light: #2a2a2a;
    --text-primary: #ffffff;
    --text-secondary: #cccccc;
    --text-muted: #999999;
    --border-color: #333333;
    --shadow-light: 0 2px 10px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 4px 20px rgba(0, 0, 0, 0.2);
    --shadow-heavy: 0 8px 30px rgba(0, 0, 0, 0.3);
    --border-radius: 10px;
    --transition: all 0.3s ease;
}

/* ===== COMMUNITY HUB CONTAINER ===== */
.community-hub {
    background-color: var(--background-dark);
    color: var(--text-primary);
    min-height: 100vh;
    padding: 20px 0;
}

.community-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* ===== SOCIAL FEED ===== */
.social-feed-container {
    max-width: 600px;
    margin: 0 auto;
}

.feed-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 20px;
    background: var(--background-light);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
}

.feed-title {
    margin: 0;
    color: var(--text-primary);
    font-size: 1.5rem;
    font-weight: 600;
}

.feed-controls .refresh-btn {
    border-color: var(--primary-color);
    color: var(--primary-color);
    transition: var(--transition);
}

.feed-controls .refresh-btn:hover {
    background-color: var(--primary-color);
    color: white;
}

/* ===== FEED FILTERS ===== */
.feed-filters {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    padding: 0 20px;
}

.filter-btn {
    background: var(--background-light);
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.9rem;
    transition: var(--transition);
    cursor: pointer;
}

.filter-btn:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.filter-btn.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* ===== POST COMPOSER ===== */
.post-composer {
    background: var(--background-light);
    border-radius: var(--border-radius);
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: var(--shadow-light);
}

.composer-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.composer-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 12px;
    object-fit: cover;
}

.composer-input {
    background: var(--background-dark);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    border-radius: var(--border-radius);
    padding: 15px;
    width: 100%;
    min-height: 100px;
    resize: vertical;
    font-family: inherit;
    font-size: 1rem;
    transition: var(--transition);
}

.composer-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.25);
}

.composer-input::placeholder {
    color: var(--text-muted);
}

.composer-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 15px;
}

.composer-tools {
    display: flex;
    gap: 10px;
}

.tool-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    padding: 8px;
    border-radius: 50%;
    cursor: pointer;
    transition: var(--transition);
}

.tool-btn:hover {
    background: var(--background-dark);
    color: var(--primary-color);
}

.post-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 20px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
}

.post-btn:hover {
    background: var(--primary-dark);
}

.post-btn:disabled {
    background: var(--text-muted);
    cursor: not-allowed;
}

/* ===== POST CARDS ===== */
.post-card {
    background: var(--background-light);
    border-radius: var(--border-radius);
    margin-bottom: 20px;
    box-shadow: var(--shadow-light);
    transition: var(--transition);
}

.post-card:hover {
    box-shadow: var(--shadow-medium);
}

.post-header {
    display: flex;
    align-items: center;
    padding: 20px 20px 0;
}

.post-avatar {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    margin-right: 12px;
    object-fit: cover;
}

.post-author-info {
    flex: 1;
}

.post-author-name {
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
    font-size: 1rem;
}

.post-author-handle {
    color: var(--text-muted);
    font-size: 0.9rem;
    margin: 0;
}

.post-timestamp {
    color: var(--text-muted);
    font-size: 0.85rem;
}

.post-content {
    padding: 15px 20px;
}

.post-text {
    color: var(--text-primary);
    line-height: 1.5;
    margin: 0;
    word-wrap: break-word;
}

.post-images {
    margin-top: 15px;
}

.post-image {
    width: 100%;
    border-radius: var(--border-radius);
    margin-bottom: 10px;
}

.post-actions {
    display: flex;
    justify-content: space-around;
    padding: 15px 20px;
    border-top: 1px solid var(--border-color);
}

.action-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    padding: 8px 16px;
    border-radius: 20px;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
}

.action-btn:hover {
    background: var(--background-dark);
    color: var(--primary-color);
}

.action-btn.liked {
    color: var(--primary-color);
}

.action-btn.liked:hover {
    color: var(--primary-dark);
}

/* ===== USER PROFILE ===== */
.user-profile-container {
    max-width: 800px;
    margin: 0 auto;
}

.profile-header {
    background: var(--background-light);
    border-radius: var(--border-radius);
    margin-bottom: 20px;
    overflow: hidden;
    box-shadow: var(--shadow-light);
}

.profile-cover {
    height: 200px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    position: relative;
}

.cover-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.profile-info {
    padding: 20px;
    position: relative;
}

.profile-avatar {
    position: absolute;
    top: -50px;
    left: 20px;
}

.avatar-image {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    border: 4px solid var(--background-light);
    object-fit: cover;
}

.verified-badge,
.barber-badge {
    position: absolute;
    bottom: 5px;
    right: 5px;
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
}

.profile-details {
    margin-left: 120px;
    margin-top: 10px;
}

.profile-name {
    color: var(--text-primary);
    margin: 0 0 10px 0;
    font-size: 1.5rem;
    font-weight: 600;
}

.profile-bio {
    color: var(--text-secondary);
    margin: 0 0 15px 0;
    line-height: 1.5;
}

.profile-meta {
    display: flex;
    gap: 20px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.profile-meta span {
    color: var(--text-muted);
    font-size: 0.9rem;
    display: flex;
    align-items: center;
}

.profile-stats {
    display: flex;
    gap: 30px;
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
}

.stat-label {
    font-size: 0.85rem;
    color: var(--text-muted);
}

.profile-actions {
    position: absolute;
    top: 20px;
    right: 20px;
}

/* ===== LOADING STATES ===== */
.loading-posts,
.error-state,
.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: var(--text-secondary);
}

.loading-posts i,
.error-state i,
.empty-state i {
    font-size: 3rem;
    margin-bottom: 15px;
    color: var(--text-muted);
}

.error-state i {
    color: var(--primary-color);
}

/* ===== NOTIFICATIONS ===== */
.new-posts-notification {
    background: var(--primary-color);
    color: white;
    padding: 10px 20px;
    border-radius: var(--border-radius);
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
}

.new-posts-notification:hover {
    background: var(--primary-dark);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .community-container {
        padding: 0 10px;
    }
    
    .feed-filters {
        padding: 0 10px;
        overflow-x: auto;
        white-space: nowrap;
    }
    
    .filter-btn {
        flex-shrink: 0;
    }
    
    .profile-details {
        margin-left: 0;
        margin-top: 60px;
    }
    
    .profile-avatar {
        position: static;
        text-align: center;
        margin-bottom: 15px;
    }
    
    .profile-actions {
        position: static;
        margin-top: 20px;
        text-align: center;
    }
    
    .profile-stats {
        justify-content: center;
    }
    
    .post-actions {
        flex-wrap: wrap;
        gap: 10px;
    }
}

@media (max-width: 480px) {
    .feed-header {
        padding: 15px;
    }
    
    .post-card {
        margin-bottom: 15px;
    }
    
    .post-header,
    .post-content,
    .post-actions {
        padding: 15px;
    }
    
    .composer-actions {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }
    
    .composer-tools {
        justify-content: center;
    }
}

/* ===== ANIMATIONS ===== */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

.post-card {
    animation: fadeIn 0.3s ease-out;
}

.new-posts-notification {
    animation: slideIn 0.3s ease-out;
}

/* ===== UTILITY CLASSES ===== */
.auth-required {
    display: none;
}

.sign-in-prompt {
    background: var(--background-light);
    border: 2px dashed var(--border-color);
    border-radius: var(--border-radius);
    padding: 40px 20px;
    text-align: center;
    margin: 20px 0;
}

.disabled {
    opacity: 0.5;
    pointer-events: none;
}

.text-primary { color: var(--text-primary) !important; }
.text-secondary { color: var(--text-secondary) !important; }
.text-muted { color: var(--text-muted) !important; }
.bg-primary { background-color: var(--primary-color) !important; }
.bg-dark { background-color: var(--background-dark) !important; }
.bg-light { background-color: var(--background-light) !important; }
