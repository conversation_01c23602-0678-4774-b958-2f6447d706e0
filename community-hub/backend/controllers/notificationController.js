/**
 * Notification Controller
 * Handles user notifications, preferences, and real-time updates
 */

const Notification = require('../models/Notification');
const User = require('../models/User');
const { validationResult } = require('express-validator');

class NotificationController {
    /**
     * Get user notifications
     */
    static async getNotifications(req, res) {
        try {
            const userId = req.user._id;
            const { 
                page = 1, 
                limit = 20, 
                type = 'all',
                unreadOnly = false 
            } = req.query;

            const skip = (page - 1) * limit;

            // Build query
            let query = {
                recipient: userId,
                isActive: true,
                isDeleted: false
            };

            if (type !== 'all') {
                query.type = type;
            }

            if (unreadOnly === 'true') {
                query.isRead = false;
            }

            const notifications = await Notification.find(query)
                .populate('sender', 'username displayName avatar isVerified')
                .populate('relatedPost', 'content images')
                .sort({ createdAt: -1 })
                .skip(skip)
                .limit(parseInt(limit));

            const total = await Notification.countDocuments(query);
            const unreadCount = await Notification.countDocuments({
                recipient: userId,
                isRead: false,
                isActive: true,
                isDeleted: false
            });

            res.json({
                success: true,
                data: {
                    notifications,
                    unreadCount,
                    pagination: {
                        page: parseInt(page),
                        limit: parseInt(limit),
                        total,
                        pages: Math.ceil(total / limit)
                    }
                }
            });

        } catch (error) {
            console.error('❌ Get notifications error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to get notifications',
                error: process.env.NODE_ENV === 'development' ? error.message : undefined
            });
        }
    }

    /**
     * Mark notification as read
     */
    static async markAsRead(req, res) {
        try {
            const { notificationId } = req.params;
            const userId = req.user._id;

            const notification = await Notification.findOneAndUpdate(
                {
                    _id: notificationId,
                    recipient: userId,
                    isActive: true,
                    isDeleted: false
                },
                { 
                    isRead: true,
                    readAt: Date.now()
                },
                { new: true }
            );

            if (!notification) {
                return res.status(404).json({
                    success: false,
                    message: 'Notification not found'
                });
            }

            res.json({
                success: true,
                message: 'Notification marked as read',
                data: { notification }
            });

        } catch (error) {
            console.error('❌ Mark notification as read error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to mark notification as read',
                error: process.env.NODE_ENV === 'development' ? error.message : undefined
            });
        }
    }

    /**
     * Mark all notifications as read
     */
    static async markAllAsRead(req, res) {
        try {
            const userId = req.user._id;

            const result = await Notification.updateMany(
                {
                    recipient: userId,
                    isRead: false,
                    isActive: true,
                    isDeleted: false
                },
                { 
                    isRead: true,
                    readAt: Date.now()
                }
            );

            res.json({
                success: true,
                message: 'All notifications marked as read',
                data: { modifiedCount: result.modifiedCount }
            });

        } catch (error) {
            console.error('❌ Mark all notifications as read error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to mark all notifications as read',
                error: process.env.NODE_ENV === 'development' ? error.message : undefined
            });
        }
    }

    /**
     * Delete notification
     */
    static async deleteNotification(req, res) {
        try {
            const { notificationId } = req.params;
            const userId = req.user._id;

            const notification = await Notification.findOneAndUpdate(
                {
                    _id: notificationId,
                    recipient: userId,
                    isActive: true,
                    isDeleted: false
                },
                { 
                    isDeleted: true,
                    deletedAt: Date.now()
                },
                { new: true }
            );

            if (!notification) {
                return res.status(404).json({
                    success: false,
                    message: 'Notification not found'
                });
            }

            res.json({
                success: true,
                message: 'Notification deleted successfully'
            });

        } catch (error) {
            console.error('❌ Delete notification error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to delete notification',
                error: process.env.NODE_ENV === 'development' ? error.message : undefined
            });
        }
    }

    /**
     * Clear all notifications
     */
    static async clearAllNotifications(req, res) {
        try {
            const userId = req.user._id;

            const result = await Notification.updateMany(
                {
                    recipient: userId,
                    isActive: true,
                    isDeleted: false
                },
                { 
                    isDeleted: true,
                    deletedAt: Date.now()
                }
            );

            res.json({
                success: true,
                message: 'All notifications cleared',
                data: { modifiedCount: result.modifiedCount }
            });

        } catch (error) {
            console.error('❌ Clear all notifications error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to clear all notifications',
                error: process.env.NODE_ENV === 'development' ? error.message : undefined
            });
        }
    }

    /**
     * Get notification preferences
     */
    static async getPreferences(req, res) {
        try {
            const userId = req.user._id;
            const user = await User.findById(userId).select('notificationPreferences');

            if (!user) {
                return res.status(404).json({
                    success: false,
                    message: 'User not found'
                });
            }

            res.json({
                success: true,
                data: { preferences: user.notificationPreferences }
            });

        } catch (error) {
            console.error('❌ Get notification preferences error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to get notification preferences',
                error: process.env.NODE_ENV === 'development' ? error.message : undefined
            });
        }
    }

    /**
     * Update notification preferences
     */
    static async updatePreferences(req, res) {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    success: false,
                    message: 'Validation failed',
                    errors: errors.array()
                });
            }

            const userId = req.user._id;
            const preferences = req.body;

            const user = await User.findByIdAndUpdate(
                userId,
                { 
                    notificationPreferences: preferences,
                    updatedAt: Date.now()
                },
                { new: true, runValidators: true }
            ).select('notificationPreferences');

            if (!user) {
                return res.status(404).json({
                    success: false,
                    message: 'User not found'
                });
            }

            res.json({
                success: true,
                message: 'Notification preferences updated successfully',
                data: { preferences: user.notificationPreferences }
            });

        } catch (error) {
            console.error('❌ Update notification preferences error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to update notification preferences',
                error: process.env.NODE_ENV === 'development' ? error.message : undefined
            });
        }
    }

    /**
     * Get unread count
     */
    static async getUnreadCount(req, res) {
        try {
            const userId = req.user._id;

            const unreadCount = await Notification.countDocuments({
                recipient: userId,
                isRead: false,
                isActive: true,
                isDeleted: false
            });

            res.json({
                success: true,
                data: { unreadCount }
            });

        } catch (error) {
            console.error('❌ Get unread count error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to get unread count',
                error: process.env.NODE_ENV === 'development' ? error.message : undefined
            });
        }
    }

    // Placeholder methods for features to be implemented later
    static async sendTestNotification(req, res) {
        res.status(501).json({
            success: false,
            message: 'Test notification feature not yet implemented'
        });
    }

    static async getRecentNotifications(req, res) {
        try {
            const userId = req.user._id;
            const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);

            const notifications = await Notification.find({
                recipient: userId,
                createdAt: { $gte: twentyFourHoursAgo },
                isActive: true,
                isDeleted: false
            })
            .populate('sender', 'username displayName avatar isVerified')
            .sort({ createdAt: -1 })
            .limit(10);

            res.json({
                success: true,
                data: { notifications }
            });

        } catch (error) {
            console.error('❌ Get recent notifications error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to get recent notifications',
                error: process.env.NODE_ENV === 'development' ? error.message : undefined
            });
        }
    }

    static async getNotificationSummary(req, res) {
        try {
            const userId = req.user._id;

            const [totalCount, unreadCount, todayCount] = await Promise.all([
                Notification.countDocuments({
                    recipient: userId,
                    isActive: true,
                    isDeleted: false
                }),
                Notification.countDocuments({
                    recipient: userId,
                    isRead: false,
                    isActive: true,
                    isDeleted: false
                }),
                Notification.countDocuments({
                    recipient: userId,
                    createdAt: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) },
                    isActive: true,
                    isDeleted: false
                })
            ]);

            res.json({
                success: true,
                data: {
                    totalCount,
                    unreadCount,
                    todayCount
                }
            });

        } catch (error) {
            console.error('❌ Get notification summary error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to get notification summary',
                error: process.env.NODE_ENV === 'development' ? error.message : undefined
            });
        }
    }
}

module.exports = NotificationController;
