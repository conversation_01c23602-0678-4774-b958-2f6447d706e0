/**
 * Comment Controller
 * Handles comment operations including nested replies, likes, and moderation
 */

const Comment = require('../models/Comment');
const Post = require('../models/Post');
const Notification = require('../models/Notification');
const { validationResult } = require('express-validator');

class CommentController {
    /**
     * Create a new comment
     */
    static async createComment(req, res) {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    success: false,
                    message: 'Validation failed',
                    errors: errors.array()
                });
            }

            const { postId, content, parentCommentId = null } = req.body;
            const author = req.user._id;

            // Check if post exists and allows comments
            const post = await Post.findOne({
                _id: postId,
                isActive: true,
                isDeleted: false
            });

            if (!post) {
                return res.status(404).json({
                    success: false,
                    message: 'Post not found'
                });
            }

            if (!post.allowComments) {
                return res.status(403).json({
                    success: false,
                    message: 'Comments are disabled for this post'
                });
            }

            // If replying to a comment, check if parent exists
            let parentComment = null;
            if (parentCommentId) {
                parentComment = await Comment.findOne({
                    _id: parentCommentId,
                    post: postId,
                    isActive: true,
                    isDeleted: false
                });

                if (!parentComment) {
                    return res.status(404).json({
                        success: false,
                        message: 'Parent comment not found'
                    });
                }
            }

            // Create comment
            const newComment = new Comment({
                post: postId,
                author,
                content,
                parentComment: parentCommentId
            });

            await newComment.save();

            // Populate author information
            await newComment.populate('author', 'username displayName avatar role isVerified');

            // Update post comments count
            const commentsCount = await Comment.countDocuments({
                post: postId,
                isActive: true,
                isDeleted: false
            });
            await Post.findByIdAndUpdate(postId, { commentsCount });

            // Create notifications
            if (parentCommentId && parentComment.author.toString() !== author.toString()) {
                // Notify parent comment author
                await Notification.createNotification({
                    recipient: parentComment.author,
                    sender: author,
                    type: 'reply_comment',
                    title: 'New Reply',
                    message: 'replied to your comment',
                    relatedPost: postId,
                    relatedComment: newComment._id
                });
            } else if (!parentCommentId && post.author.toString() !== author.toString()) {
                // Notify post author
                await Notification.createCommentNotification(postId, author, post.author);
            }

            // Emit real-time event
            const io = req.app.get('io');
            if (io) {
                io.to(`post-${postId}`).emit('comment-added', {
                    comment: newComment,
                    postId
                });
            }

            console.log(`✅ New comment created by ${req.user.username} on post ${postId}`);

            res.status(201).json({
                success: true,
                message: 'Comment created successfully',
                data: {
                    comment: newComment
                }
            });

        } catch (error) {
            console.error('❌ Create comment error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to create comment',
                error: process.env.NODE_ENV === 'development' ? error.message : undefined
            });
        }
    }

    /**
     * Get comments for a post
     */
    static async getComments(req, res) {
        try {
            const { postId } = req.params;
            const { 
                page = 1, 
                limit = 20, 
                sortBy = 'createdAt', 
                sortOrder = 'desc',
                includeReplies = false 
            } = req.query;

            const skip = (page - 1) * limit;
            const sort = { [sortBy]: sortOrder === 'desc' ? -1 : 1 };

            // Build query
            let query = {
                post: postId,
                isActive: true,
                isDeleted: false,
                moderationStatus: 'approved'
            };

            // Only get top-level comments if not including replies
            if (!includeReplies) {
                query.parentComment = null;
            }

            const comments = await Comment.find(query)
                .populate('author', 'username displayName avatar role isVerified')
                .populate({
                    path: 'replies',
                    populate: {
                        path: 'author',
                        select: 'username displayName avatar role isVerified'
                    },
                    match: { isActive: true, isDeleted: false },
                    options: { sort: { createdAt: 1 }, limit: 3 } // Show first 3 replies
                })
                .sort(sort)
                .limit(parseInt(limit))
                .skip(skip)
                .lean();

            // Add user interaction data if authenticated
            if (req.user) {
                comments.forEach(comment => {
                    comment.isLikedByUser = comment.likes.some(like => 
                        like.user.toString() === req.user._id.toString()
                    );
                    
                    // Check replies too
                    if (comment.replies) {
                        comment.replies.forEach(reply => {
                            reply.isLikedByUser = reply.likes.some(like => 
                                like.user.toString() === req.user._id.toString()
                            );
                        });
                    }
                });
            }

            const totalComments = await Comment.countDocuments(query);
            const hasMore = skip + comments.length < totalComments;

            res.json({
                success: true,
                data: {
                    comments,
                    pagination: {
                        page: parseInt(page),
                        limit: parseInt(limit),
                        total: totalComments,
                        hasMore
                    }
                }
            });

        } catch (error) {
            console.error('❌ Get comments error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to get comments'
            });
        }
    }

    /**
     * Get replies for a comment
     */
    static async getReplies(req, res) {
        try {
            const { commentId } = req.params;
            const { page = 1, limit = 10 } = req.query;

            const skip = (page - 1) * limit;

            const replies = await Comment.find({
                parentComment: commentId,
                isActive: true,
                isDeleted: false,
                moderationStatus: 'approved'
            })
            .populate('author', 'username displayName avatar role isVerified')
            .sort({ createdAt: 1 })
            .limit(parseInt(limit))
            .skip(skip)
            .lean();

            // Add user interaction data
            if (req.user) {
                replies.forEach(reply => {
                    reply.isLikedByUser = reply.likes.some(like => 
                        like.user.toString() === req.user._id.toString()
                    );
                });
            }

            const totalReplies = await Comment.countDocuments({
                parentComment: commentId,
                isActive: true,
                isDeleted: false
            });

            res.json({
                success: true,
                data: {
                    replies,
                    pagination: {
                        page: parseInt(page),
                        limit: parseInt(limit),
                        total: totalReplies,
                        hasMore: skip + replies.length < totalReplies
                    }
                }
            });

        } catch (error) {
            console.error('❌ Get replies error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to get replies'
            });
        }
    }

    /**
     * Update comment
     */
    static async updateComment(req, res) {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    success: false,
                    message: 'Validation failed',
                    errors: errors.array()
                });
            }

            const { commentId } = req.params;
            const { content } = req.body;

            const comment = await Comment.findOne({
                _id: commentId,
                isActive: true,
                isDeleted: false
            });

            if (!comment) {
                return res.status(404).json({
                    success: false,
                    message: 'Comment not found'
                });
            }

            // Check ownership
            if (comment.author.toString() !== req.user._id.toString() && req.user.role !== 'admin') {
                return res.status(403).json({
                    success: false,
                    message: 'Access denied'
                });
            }

            // Store original content in edit history
            comment.editHistory.push({
                content: comment.content,
                editedAt: Date.now()
            });

            // Update content
            comment.content = content;
            comment.isEdited = true;
            comment.lastEditedAt = Date.now();

            await comment.save();
            await comment.populate('author', 'username displayName avatar role isVerified');

            res.json({
                success: true,
                message: 'Comment updated successfully',
                data: {
                    comment
                }
            });

        } catch (error) {
            console.error('❌ Update comment error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to update comment'
            });
        }
    }

    /**
     * Delete comment
     */
    static async deleteComment(req, res) {
        try {
            const { commentId } = req.params;

            const comment = await Comment.findOne({
                _id: commentId,
                isActive: true,
                isDeleted: false
            });

            if (!comment) {
                return res.status(404).json({
                    success: false,
                    message: 'Comment not found'
                });
            }

            // Check ownership or admin
            if (comment.author.toString() !== req.user._id.toString() && req.user.role !== 'admin') {
                return res.status(403).json({
                    success: false,
                    message: 'Access denied'
                });
            }

            // Soft delete
            comment.isDeleted = true;
            comment.isActive = false;
            comment.deletedAt = Date.now();
            comment.deletedBy = req.user._id;
            await comment.save();

            // Update post comments count
            const commentsCount = await Comment.countDocuments({
                post: comment.post,
                isActive: true,
                isDeleted: false
            });
            await Post.findByIdAndUpdate(comment.post, { commentsCount });

            res.json({
                success: true,
                message: 'Comment deleted successfully'
            });

        } catch (error) {
            console.error('❌ Delete comment error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to delete comment'
            });
        }
    }

    /**
     * Like/Unlike comment
     */
    static async toggleLike(req, res) {
        try {
            const { commentId } = req.params;
            const userId = req.user._id;

            const comment = await Comment.findOne({
                _id: commentId,
                isActive: true,
                isDeleted: false
            });

            if (!comment) {
                return res.status(404).json({
                    success: false,
                    message: 'Comment not found'
                });
            }

            const existingLikeIndex = comment.likes.findIndex(like => 
                like.user.toString() === userId.toString()
            );

            let action;
            if (existingLikeIndex > -1) {
                // Unlike
                comment.likes.splice(existingLikeIndex, 1);
                action = 'unliked';
            } else {
                // Like
                comment.likes.push({ user: userId });
                action = 'liked';

                // Create notification for comment author
                if (comment.author.toString() !== userId.toString()) {
                    await Notification.createNotification({
                        recipient: comment.author,
                        sender: userId,
                        type: 'like_comment',
                        title: 'Comment Liked',
                        message: 'liked your comment',
                        relatedPost: comment.post,
                        relatedComment: commentId
                    });
                }
            }

            comment.likesCount = comment.likes.length;
            await comment.save();

            res.json({
                success: true,
                message: `Comment ${action} successfully`,
                data: {
                    action,
                    likesCount: comment.likesCount,
                    isLiked: action === 'liked'
                }
            });

        } catch (error) {
            console.error('❌ Toggle comment like error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to toggle comment like'
            });
        }
    }

    /**
     * Report comment
     */
    static async reportComment(req, res) {
        try {
            const { commentId } = req.params;
            const { reason, description } = req.body;

            const comment = await Comment.findById(commentId);

            if (!comment) {
                return res.status(404).json({
                    success: false,
                    message: 'Comment not found'
                });
            }

            // Check if user already reported this comment
            const existingReport = comment.reports.find(report => 
                report.reporter.toString() === req.user._id.toString()
            );

            if (existingReport) {
                return res.status(400).json({
                    success: false,
                    message: 'You have already reported this comment'
                });
            }

            // Add report
            comment.reports.push({
                reporter: req.user._id,
                reason,
                description
            });

            comment.reportCount = comment.reports.length;
            comment.isReported = true;

            // Auto-flag if too many reports
            if (comment.reportCount >= 3) {
                comment.moderationStatus = 'flagged';
            }

            await comment.save();

            res.json({
                success: true,
                message: 'Comment reported successfully'
            });

        } catch (error) {
            console.error('❌ Report comment error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to report comment'
            });
        }
    }

    /**
     * Get user's comments
     */
    static async getUserComments(req, res) {
        try {
            const { userId } = req.params;
            const { page = 1, limit = 20 } = req.query;

            const skip = (page - 1) * limit;

            const comments = await Comment.find({
                author: userId,
                isActive: true,
                isDeleted: false
            })
            .populate('post', 'content author createdAt')
            .populate('author', 'username displayName avatar role isVerified')
            .sort({ createdAt: -1 })
            .limit(parseInt(limit))
            .skip(skip)
            .lean();

            const totalComments = await Comment.countDocuments({
                author: userId,
                isActive: true,
                isDeleted: false
            });

            res.json({
                success: true,
                data: {
                    comments,
                    pagination: {
                        page: parseInt(page),
                        limit: parseInt(limit),
                        total: totalComments,
                        hasMore: skip + comments.length < totalComments
                    }
                }
            });

        } catch (error) {
            console.error('❌ Get user comments error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to get user comments'
            });
        }
    }
}

module.exports = CommentController;
