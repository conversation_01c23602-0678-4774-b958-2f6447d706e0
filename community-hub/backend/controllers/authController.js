/**
 * Authentication Controller
 * Handles user authentication, registration, and session management
 */

const User = require('../models/User');
const { generateJWT, generateRefreshToken, hashPassword, comparePassword } = require('../config/auth');
const { validationResult } = require('express-validator');

class AuthController {
    /**
     * Google OAuth callback handler
     */
    static async googleCallback(req, res) {
        try {
            const user = req.user;
            
            if (!user) {
                return res.redirect(`${process.env.FRONTEND_URL}/auth/error?message=Authentication failed`);
            }

            // Generate JWT tokens
            const accessToken = generateJWT(user);
            const refreshToken = generateRefreshToken(user);

            // Update user login statistics
            user.loginCount += 1;
            user.lastLoginAt = Date.now();
            await user.save();

            console.log(`✅ User authenticated via Google: ${user.email}`);

            // Redirect to frontend with tokens
            const redirectUrl = `${process.env.FRONTEND_URL}/auth/success?token=${accessToken}&refresh=${refreshToken}`;
            res.redirect(redirectUrl);

        } catch (error) {
            console.error('❌ Google OAuth callback error:', error);
            res.redirect(`${process.env.FRONTEND_URL}/auth/error?message=Authentication failed`);
        }
    }

    /**
     * Register new user with email/password
     */
    static async register(req, res) {
        try {
            // Check validation errors
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    success: false,
                    message: 'Validation failed',
                    errors: errors.array()
                });
            }

            const { email, password, username, displayName, firstName, lastName } = req.body;

            // Check if user already exists
            const existingUser = await User.findOne({
                $or: [{ email }, { username }]
            });

            if (existingUser) {
                return res.status(400).json({
                    success: false,
                    message: existingUser.email === email ? 'Email already registered' : 'Username already taken'
                });
            }

            // Create new user
            const newUser = new User({
                email,
                username,
                displayName,
                firstName,
                lastName,
                password, // Will be hashed by pre-save middleware
                authProvider: 'local',
                role: 'user',
                preferences: {
                    notifications: true,
                    emailUpdates: true,
                    privacy: 'public'
                }
            });

            await newUser.save();

            // Generate tokens
            const accessToken = generateJWT(newUser);
            const refreshToken = generateRefreshToken(newUser);

            // Remove password from response
            const userResponse = newUser.toObject();
            delete userResponse.password;

            console.log(`✅ New user registered: ${newUser.email}`);

            res.status(201).json({
                success: true,
                message: 'User registered successfully',
                data: {
                    user: userResponse,
                    accessToken,
                    refreshToken
                }
            });

        } catch (error) {
            console.error('❌ Registration error:', error);
            res.status(500).json({
                success: false,
                message: 'Registration failed',
                error: process.env.NODE_ENV === 'development' ? error.message : undefined
            });
        }
    }

    /**
     * Login with email/password
     */
    static async login(req, res) {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    success: false,
                    message: 'Validation failed',
                    errors: errors.array()
                });
            }

            const { email, password } = req.body;

            // Find user and include password for comparison
            const user = await User.findOne({ email }).select('+password');

            if (!user) {
                return res.status(401).json({
                    success: false,
                    message: 'Invalid email or password'
                });
            }

            // Check password
            const isPasswordValid = await user.comparePassword(password);

            if (!isPasswordValid) {
                return res.status(401).json({
                    success: false,
                    message: 'Invalid email or password'
                });
            }

            // Check if user is active
            if (!user.isActive) {
                return res.status(401).json({
                    success: false,
                    message: 'Account is deactivated'
                });
            }

            // Check if user is banned
            if (user.isBanned) {
                const banMessage = user.banExpiresAt && user.banExpiresAt > Date.now() 
                    ? `Account is banned until ${user.banExpiresAt.toDateString()}`
                    : 'Account is permanently banned';
                    
                return res.status(403).json({
                    success: false,
                    message: banMessage,
                    reason: user.banReason
                });
            }

            // Update login statistics
            user.loginCount += 1;
            user.lastLoginAt = Date.now();
            await user.save();

            // Generate tokens
            const accessToken = generateJWT(user);
            const refreshToken = generateRefreshToken(user);

            // Remove password from response
            const userResponse = user.toObject();
            delete userResponse.password;

            console.log(`✅ User logged in: ${user.email}`);

            res.json({
                success: true,
                message: 'Login successful',
                data: {
                    user: userResponse,
                    accessToken,
                    refreshToken
                }
            });

        } catch (error) {
            console.error('❌ Login error:', error);
            res.status(500).json({
                success: false,
                message: 'Login failed',
                error: process.env.NODE_ENV === 'development' ? error.message : undefined
            });
        }
    }

    /**
     * Refresh access token
     */
    static async refreshToken(req, res) {
        try {
            const { refreshToken } = req.body;

            if (!refreshToken) {
                return res.status(400).json({
                    success: false,
                    message: 'Refresh token required'
                });
            }

            // Verify refresh token
            const jwt = require('jsonwebtoken');
            const decoded = jwt.verify(refreshToken, process.env.REFRESH_TOKEN_SECRET, {
                issuer: 'barber-brothers-community',
                audience: 'barber-brothers-users'
            });

            // Get user
            const user = await User.findById(decoded.id);

            if (!user || !user.isActive || user.isBanned) {
                return res.status(401).json({
                    success: false,
                    message: 'Invalid refresh token'
                });
            }

            // Generate new access token
            const newAccessToken = generateJWT(user);

            res.json({
                success: true,
                message: 'Token refreshed successfully',
                data: {
                    accessToken: newAccessToken
                }
            });

        } catch (error) {
            console.error('❌ Token refresh error:', error);
            
            if (error.name === 'JsonWebTokenError' || error.name === 'TokenExpiredError') {
                return res.status(401).json({
                    success: false,
                    message: 'Invalid refresh token'
                });
            }

            res.status(500).json({
                success: false,
                message: 'Token refresh failed'
            });
        }
    }

    /**
     * Logout user
     */
    static async logout(req, res) {
        try {
            const user = req.user;

            // Update user status
            if (user) {
                user.isOnline = false;
                await user.save();
            }

            res.json({
                success: true,
                message: 'Logout successful'
            });

        } catch (error) {
            console.error('❌ Logout error:', error);
            res.status(500).json({
                success: false,
                message: 'Logout failed'
            });
        }
    }

    /**
     * Get current user profile
     */
    static async getProfile(req, res) {
        try {
            const user = req.user;

            // Get user with populated fields
            const userProfile = await User.findById(user._id)
                .populate('followers', 'username displayName avatar')
                .populate('following', 'username displayName avatar');

            res.json({
                success: true,
                data: {
                    user: userProfile
                }
            });

        } catch (error) {
            console.error('❌ Get profile error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to get user profile'
            });
        }
    }

    /**
     * Update user profile
     */
    static async updateProfile(req, res) {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    success: false,
                    message: 'Validation failed',
                    errors: errors.array()
                });
            }

            const user = req.user;
            const updates = req.body;

            // Fields that can be updated
            const allowedUpdates = [
                'displayName', 'firstName', 'lastName', 'bio', 'location', 
                'website', 'preferences'
            ];

            // Filter updates to only allowed fields
            const filteredUpdates = {};
            Object.keys(updates).forEach(key => {
                if (allowedUpdates.includes(key)) {
                    filteredUpdates[key] = updates[key];
                }
            });

            // Handle nested preferences updates
            if (updates.preferences) {
                filteredUpdates.preferences = {
                    ...user.preferences,
                    ...updates.preferences
                };
            }

            // Update user
            const updatedUser = await User.findByIdAndUpdate(
                user._id,
                filteredUpdates,
                { new: true, runValidators: true }
            );

            res.json({
                success: true,
                message: 'Profile updated successfully',
                data: {
                    user: updatedUser
                }
            });

        } catch (error) {
            console.error('❌ Update profile error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to update profile',
                error: process.env.NODE_ENV === 'development' ? error.message : undefined
            });
        }
    }

    /**
     * Change password
     */
    static async changePassword(req, res) {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    success: false,
                    message: 'Validation failed',
                    errors: errors.array()
                });
            }

            const { currentPassword, newPassword } = req.body;
            const user = await User.findById(req.user._id).select('+password');

            // Check if user has a password (OAuth users might not)
            if (!user.password) {
                return res.status(400).json({
                    success: false,
                    message: 'Cannot change password for OAuth accounts'
                });
            }

            // Verify current password
            const isCurrentPasswordValid = await user.comparePassword(currentPassword);

            if (!isCurrentPasswordValid) {
                return res.status(400).json({
                    success: false,
                    message: 'Current password is incorrect'
                });
            }

            // Update password
            user.password = newPassword; // Will be hashed by pre-save middleware
            await user.save();

            res.json({
                success: true,
                message: 'Password changed successfully'
            });

        } catch (error) {
            console.error('❌ Change password error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to change password'
            });
        }
    }

    /**
     * Verify user account
     */
    static async verifyAccount(req, res) {
        try {
            const { token } = req.params;

            const user = await User.findOne({
                emailVerificationToken: token,
                emailVerificationExpires: { $gt: Date.now() }
            });

            if (!user) {
                return res.status(400).json({
                    success: false,
                    message: 'Invalid or expired verification token'
                });
            }

            user.isVerified = true;
            user.emailVerificationToken = undefined;
            user.emailVerificationExpires = undefined;
            await user.save();

            res.json({
                success: true,
                message: 'Account verified successfully'
            });

        } catch (error) {
            console.error('❌ Account verification error:', error);
            res.status(500).json({
                success: false,
                message: 'Account verification failed'
            });
        }
    }

    /**
     * Check authentication status
     */
    static async checkAuth(req, res) {
        try {
            const user = req.user;

            if (!user) {
                return res.status(401).json({
                    success: false,
                    message: 'Not authenticated'
                });
            }

            res.json({
                success: true,
                data: {
                    user,
                    authenticated: true
                }
            });

        } catch (error) {
            console.error('❌ Check auth error:', error);
            res.status(500).json({
                success: false,
                message: 'Authentication check failed'
            });
        }
    }

    /**
     * Google OAuth authentication
     */
    static async googleAuth(req, res) {
        try {
            const { token } = req.body;

            if (!token) {
                return res.status(400).json({
                    success: false,
                    message: 'Google token is required'
                });
            }

            // This would typically verify the Google token
            // For now, we'll return a placeholder response
            res.status(501).json({
                success: false,
                message: 'Google OAuth integration not yet implemented. Please use email/password authentication for now.'
            });

        } catch (error) {
            console.error('❌ Google auth error:', error);
            res.status(500).json({
                success: false,
                message: 'Google authentication failed',
                error: process.env.NODE_ENV === 'development' ? error.message : undefined
            });
        }
    }

    /**
     * Get current user profile
     */
    static async getCurrentUser(req, res) {
        try {
            const user = req.user;

            if (!user) {
                return res.status(401).json({
                    success: false,
                    message: 'User not authenticated'
                });
            }

            // Remove sensitive information
            const userResponse = user.toObject();
            delete userResponse.password;
            delete userResponse.refreshTokens;
            delete userResponse.emailVerificationToken;
            delete userResponse.passwordResetToken;

            res.json({
                success: true,
                data: { user: userResponse }
            });

        } catch (error) {
            console.error('❌ Get current user error:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to get current user',
                error: process.env.NODE_ENV === 'development' ? error.message : undefined
            });
        }
    }
}

module.exports = AuthController;
