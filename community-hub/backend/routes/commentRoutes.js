/**
 * Comment Routes for Barber Brothers Community Hub
 * Handles comment creation, retrieval, updates, and deletion
 */

const express = require('express');
const router = express.Router();
const commentController = require('../controllers/commentController');
const authMiddleware = require('../middleware/auth');
const { body, query } = require('express-validator');

// Validation middleware
const createCommentValidation = [
    body('content')
        .trim()
        .isLength({ min: 1, max: 1000 })
        .withMessage('Comment content must be between 1 and 1000 characters'),
    body('postId')
        .isMongoId()
        .withMessage('Invalid post ID'),
    body('parentId')
        .optional()
        .isMongoId()
        .withMessage('Invalid parent comment ID')
];

const updateCommentValidation = [
    body('content')
        .trim()
        .isLength({ min: 1, max: 1000 })
        .withMessage('Comment content must be between 1 and 1000 characters')
];

const paginationValidation = [
    query('page')
        .optional()
        .isInt({ min: 1 })
        .withMessage('Page must be a positive integer'),
    query('limit')
        .optional()
        .isInt({ min: 1, max: 50 })
        .withMessage('Limit must be between 1 and 50'),
    query('sort')
        .optional()
        .isIn(['newest', 'oldest', 'popular'])
        .withMessage('Sort must be one of: newest, oldest, popular')
];

// @route   GET /api/comments/post/:postId
// @desc    Get comments for a specific post
// @access  Public
router.get('/post/:postId', paginationValidation, commentController.getCommentsByPost);

// @route   GET /api/comments/:id/replies
// @desc    Get replies to a specific comment
// @access  Public
router.get('/:id/replies', paginationValidation, commentController.getCommentReplies);

// @route   GET /api/comments/:id
// @desc    Get single comment by ID
// @access  Public
router.get('/:id', commentController.getComment);

// @route   POST /api/comments
// @desc    Create a new comment
// @access  Private
router.post('/', authMiddleware, createCommentValidation, commentController.createComment);

// @route   PUT /api/comments/:id
// @desc    Update a comment
// @access  Private
router.put('/:id', authMiddleware, updateCommentValidation, commentController.updateComment);

// @route   DELETE /api/comments/:id
// @desc    Delete a comment
// @access  Private
router.delete('/:id', authMiddleware, commentController.deleteComment);

// @route   POST /api/comments/:id/like
// @desc    Like/unlike a comment
// @access  Private
router.post('/:id/like', authMiddleware, commentController.toggleLike);

// @route   GET /api/comments/:id/likes
// @desc    Get comment likes
// @access  Public
router.get('/:id/likes', paginationValidation, commentController.getCommentLikes);

// @route   POST /api/comments/:id/report
// @desc    Report a comment
// @access  Private
router.post('/:id/report', authMiddleware, [
    body('reason')
        .trim()
        .isLength({ min: 1, max: 500 })
        .withMessage('Report reason must be between 1 and 500 characters'),
    body('category')
        .isIn(['spam', 'harassment', 'inappropriate', 'other'])
        .withMessage('Invalid report category')
], commentController.reportComment);

// @route   GET /api/comments/user/:userId
// @desc    Get comments by specific user
// @access  Public
router.get('/user/:userId', paginationValidation, commentController.getCommentsByUser);

// @route   GET /api/comments/recent/me
// @desc    Get recent comments by authenticated user
// @access  Private
router.get('/recent/me', authMiddleware, paginationValidation, commentController.getMyRecentComments);

module.exports = router;
