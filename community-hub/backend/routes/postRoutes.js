/**
 * Post Routes for Barber Brothers Community Hub
 * Handles post creation, retrieval, updates, and deletion
 */

const express = require('express');
const router = express.Router();
const postController = require('../controllers/postController');
const authMiddleware = require('../middleware/auth');
const uploadMiddleware = require('../middleware/upload');
const { body, query } = require('express-validator');

// Validation middleware
const createPostValidation = [
    body('content')
        .trim()
        .isLength({ min: 1, max: 2000 })
        .withMessage('Post content must be between 1 and 2000 characters'),
    body('tags')
        .optional()
        .isArray()
        .withMessage('Tags must be an array'),
    body('tags.*')
        .optional()
        .trim()
        .isLength({ min: 1, max: 50 })
        .withMessage('Each tag must be between 1 and 50 characters')
];

const updatePostValidation = [
    body('content')
        .optional()
        .trim()
        .isLength({ min: 1, max: 2000 })
        .withMessage('Post content must be between 1 and 2000 characters'),
    body('tags')
        .optional()
        .isArray()
        .withMessage('Tags must be an array')
];

const paginationValidation = [
    query('page')
        .optional()
        .isInt({ min: 1 })
        .withMessage('Page must be a positive integer'),
    query('limit')
        .optional()
        .isInt({ min: 1, max: 50 })
        .withMessage('Limit must be between 1 and 50'),
    query('sort')
        .optional()
        .isIn(['newest', 'oldest', 'popular', 'trending'])
        .withMessage('Sort must be one of: newest, oldest, popular, trending')
];

// @route   GET /api/posts
// @desc    Get all posts with pagination and filtering
// @access  Public
router.get('/', paginationValidation, postController.getPosts);

// @route   GET /api/posts/feed
// @desc    Get personalized feed for authenticated user
// @access  Private
router.get('/feed', authMiddleware, paginationValidation, postController.getFeed);

// @route   GET /api/posts/trending
// @desc    Get trending posts
// @access  Public
router.get('/trending', paginationValidation, postController.getTrendingPosts);

// @route   GET /api/posts/user/:userId
// @desc    Get posts by specific user
// @access  Public
router.get('/user/:userId', paginationValidation, postController.getPostsByUser);

// @route   GET /api/posts/tag/:tag
// @desc    Get posts by tag
// @access  Public
router.get('/tag/:tag', paginationValidation, postController.getPostsByTag);

// @route   GET /api/posts/search
// @desc    Search posts
// @access  Public
router.get('/search', [
    query('q')
        .trim()
        .isLength({ min: 1, max: 100 })
        .withMessage('Search query must be between 1 and 100 characters'),
    ...paginationValidation
], postController.searchPosts);

// @route   GET /api/posts/:id
// @desc    Get single post by ID
// @access  Public
router.get('/:id', postController.getPost);

// @route   POST /api/posts
// @desc    Create a new post
// @access  Private
router.post('/', authMiddleware, uploadMiddleware.array('images', 5), createPostValidation, postController.createPost);

// @route   PUT /api/posts/:id
// @desc    Update a post
// @access  Private
router.put('/:id', authMiddleware, uploadMiddleware.array('images', 5), updatePostValidation, postController.updatePost);

// @route   DELETE /api/posts/:id
// @desc    Delete a post
// @access  Private
router.delete('/:id', authMiddleware, postController.deletePost);

// @route   POST /api/posts/:id/like
// @desc    Like/unlike a post
// @access  Private
router.post('/:id/like', authMiddleware, postController.toggleLike);

// @route   GET /api/posts/:id/likes
// @desc    Get post likes
// @access  Public
router.get('/:id/likes', paginationValidation, postController.getPostLikes);

// @route   POST /api/posts/:id/share
// @desc    Share a post
// @access  Private
router.post('/:id/share', authMiddleware, postController.sharePost);

// @route   POST /api/posts/:id/report
// @desc    Report a post
// @access  Private
router.post('/:id/report', authMiddleware, [
    body('reason')
        .trim()
        .isLength({ min: 1, max: 500 })
        .withMessage('Report reason must be between 1 and 500 characters'),
    body('category')
        .isIn(['spam', 'harassment', 'inappropriate', 'copyright', 'other'])
        .withMessage('Invalid report category')
], postController.reportPost);

// @route   POST /api/posts/:id/bookmark
// @desc    Bookmark/unbookmark a post
// @access  Private
router.post('/:id/bookmark', authMiddleware, postController.toggleBookmark);

// @route   GET /api/posts/bookmarks/me
// @desc    Get user's bookmarked posts
// @access  Private
router.get('/bookmarks/me', authMiddleware, paginationValidation, postController.getBookmarkedPosts);

module.exports = router;
