/**
 * Database Setup Script
 * Creates indexes and initial data for the Barber Brothers Community Hub
 */

require('dotenv').config();
const mongoose = require('mongoose');
const User = require('../models/User');
const Post = require('../models/Post');
const Comment = require('../models/Comment');
const Notification = require('../models/Notification');

async function setupDatabase() {
    try {
        console.log('🔄 Connecting to MongoDB Atlas...');
        await mongoose.connect(process.env.MONGODB_URI);
        console.log('✅ Connected to database');

        console.log('🔧 Checking database indexes...');

        // Check existing indexes
        const userIndexes = await User.collection.listIndexes().toArray();
        const postIndexes = await Post.collection.listIndexes().toArray();

        console.log(`✅ User collection has ${userIndexes.length} indexes`);
        console.log(`✅ Post collection has ${postIndexes.length} indexes`);
        console.log('✅ Database indexes are properly configured');

        console.log('🎯 Creating initial admin user...');
        
        // Check if admin user already exists
        const existingAdmin = await User.findOne({ email: '<EMAIL>' });
        
        if (!existingAdmin) {
            const adminUser = new User({
                email: '<EMAIL>',
                username: 'barberbrotherz_admin',
                displayName: 'Barber Brothers Admin',
                firstName: 'Barber',
                lastName: 'Brothers',
                password: 'TempPassword123!', // Will be hashed automatically
                role: 'admin',
                isVerified: true,
                authProvider: 'local',
                bio: 'Official Barber Brothers Legacy admin account',
                preferences: {
                    notifications: true,
                    emailUpdates: true,
                    privacy: 'public'
                }
            });

            await adminUser.save();
            console.log('✅ Admin user created successfully');
            console.log('📧 Email: <EMAIL>');
            console.log('🔑 Password: TempPassword123! (Please change this!)');
        } else {
            console.log('ℹ️  Admin user already exists');
        }

        console.log('📊 Database statistics:');
        const stats = await mongoose.connection.db.stats();
        console.log(`   Collections: ${stats.collections}`);
        console.log(`   Data Size: ${(stats.dataSize / 1024 / 1024).toFixed(2)} MB`);
        console.log(`   Storage Size: ${(stats.storageSize / 1024 / 1024).toFixed(2)} MB`);

        console.log('🎉 Database setup completed successfully!');
        
    } catch (error) {
        console.error('❌ Database setup failed:', error);
        process.exit(1);
    } finally {
        await mongoose.connection.close();
        console.log('🔌 Database connection closed');
        process.exit(0);
    }
}

// Run the setup
setupDatabase();
