/**
 * Authentication Configuration
 * Passport.js setup for Google OAuth and JWT strategies
 */

const passport = require('passport');
const GoogleStrategy = require('passport-google-oauth20').Strategy;
const JwtStrategy = require('passport-jwt').Strategy;
const ExtractJwt = require('passport-jwt').ExtractJwt;
const User = require('../models/User');

// JWT Strategy Configuration
const jwtOptions = {
    jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
    secretOrKey: process.env.JWT_SECRET,
    issuer: 'barber-brothers-community',
    audience: 'barber-brothers-users'
};

passport.use(new JwtStrategy(jwtOptions, async (payload, done) => {
    try {
        // Find user by ID from JWT payload
        const user = await User.findById(payload.id).select('-password');
        
        if (user) {
            return done(null, user);
        } else {
            return done(null, false);
        }
    } catch (error) {
        console.error('❌ JWT Strategy error:', error);
        return done(error, false);
    }
}));

// Google OAuth Strategy Configuration
passport.use(new GoogleStrategy({
    clientID: process.env.GOOGLE_CLIENT_ID,
    clientSecret: process.env.GOOGLE_CLIENT_SECRET,
    callbackURL: process.env.GOOGLE_CALLBACK_URL || '/api/auth/google/callback'
}, async (accessToken, refreshToken, profile, done) => {
    try {
        console.log('🔐 Google OAuth callback received for:', profile.emails[0].value);

        // Check if user already exists
        let user = await User.findOne({ 
            $or: [
                { googleId: profile.id },
                { email: profile.emails[0].value }
            ]
        });

        if (user) {
            // Update existing user with Google info if not already set
            if (!user.googleId) {
                user.googleId = profile.id;
                user.avatar = user.avatar || profile.photos[0]?.value;
                await user.save();
            }
            
            console.log('✅ Existing user signed in:', user.email);
            return done(null, user);
        } else {
            // Create new user
            const newUser = new User({
                googleId: profile.id,
                email: profile.emails[0].value,
                username: profile.emails[0].value.split('@')[0],
                displayName: profile.displayName,
                firstName: profile.name.givenName,
                lastName: profile.name.familyName,
                avatar: profile.photos[0]?.value,
                isVerified: profile.emails[0].verified,
                authProvider: 'google',
                role: 'user',
                preferences: {
                    notifications: true,
                    emailUpdates: true,
                    privacy: 'public'
                }
            });

            await newUser.save();
            console.log('✅ New user created:', newUser.email);
            return done(null, newUser);
        }
    } catch (error) {
        console.error('❌ Google OAuth error:', error);
        return done(error, null);
    }
}));

// Serialize user for session
passport.serializeUser((user, done) => {
    done(null, user._id);
});

// Deserialize user from session
passport.deserializeUser(async (id, done) => {
    try {
        const user = await User.findById(id).select('-password');
        done(null, user);
    } catch (error) {
        done(error, null);
    }
});

// JWT Token Generation
const generateJWT = (user) => {
    const jwt = require('jsonwebtoken');
    
    const payload = {
        id: user._id,
        email: user.email,
        role: user.role,
        iat: Math.floor(Date.now() / 1000)
    };

    const options = {
        expiresIn: process.env.JWT_EXPIRES_IN || '7d',
        issuer: 'barber-brothers-community',
        audience: 'barber-brothers-users'
    };

    return jwt.sign(payload, process.env.JWT_SECRET, options);
};

// JWT Token Verification
const verifyJWT = (token) => {
    const jwt = require('jsonwebtoken');
    
    try {
        return jwt.verify(token, process.env.JWT_SECRET, {
            issuer: 'barber-brothers-community',
            audience: 'barber-brothers-users'
        });
    } catch (error) {
        throw new Error('Invalid or expired token');
    }
};

// Refresh Token Generation
const generateRefreshToken = (user) => {
    const jwt = require('jsonwebtoken');
    
    const payload = {
        id: user._id,
        type: 'refresh'
    };

    const options = {
        expiresIn: process.env.REFRESH_TOKEN_EXPIRES_IN || '30d',
        issuer: 'barber-brothers-community',
        audience: 'barber-brothers-users'
    };

    return jwt.sign(payload, process.env.REFRESH_TOKEN_SECRET, options);
};

// Password hashing utilities
const bcrypt = require('bcryptjs');

const hashPassword = async (password) => {
    const saltRounds = 12;
    return await bcrypt.hash(password, saltRounds);
};

const comparePassword = async (password, hashedPassword) => {
    return await bcrypt.compare(password, hashedPassword);
};

// Rate limiting for authentication
const authRateLimit = require('express-rate-limit')({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 5, // Limit each IP to 5 requests per windowMs for auth endpoints
    message: {
        error: 'Too many authentication attempts, please try again later.'
    },
    standardHeaders: true,
    legacyHeaders: false,
    skipSuccessfulRequests: true
});

// OAuth state generation for CSRF protection
const generateOAuthState = () => {
    const crypto = require('crypto');
    return crypto.randomBytes(32).toString('hex');
};

// Validate OAuth state
const validateOAuthState = (sessionState, receivedState) => {
    return sessionState === receivedState;
};

module.exports = {
    passport,
    generateJWT,
    verifyJWT,
    generateRefreshToken,
    hashPassword,
    comparePassword,
    authRateLimit,
    generateOAuthState,
    validateOAuthState
};
