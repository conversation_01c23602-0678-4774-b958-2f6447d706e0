/**
 * Authentication Middleware
 * JWT token verification and user authorization
 */

const jwt = require('jsonwebtoken');
const User = require('../models/User');

// Verify JWT token
const authenticateToken = async (req, res, next) => {
    try {
        const authHeader = req.headers.authorization;
        const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN
        
        if (!token) {
            return res.status(401).json({
                success: false,
                message: 'Access token required'
            });
        }
        
        // Verify token
        const decoded = jwt.verify(token, process.env.JWT_SECRET, {
            issuer: 'barber-brothers-community',
            audience: 'barber-brothers-users'
        });
        
        // Get user from database
        const user = await User.findById(decoded.id).select('-password');
        
        if (!user) {
            return res.status(401).json({
                success: false,
                message: 'User not found'
            });
        }
        
        // Check if user is active
        if (!user.isActive) {
            return res.status(401).json({
                success: false,
                message: 'Account is deactivated'
            });
        }
        
        // Check if user is banned
        if (user.isBanned) {
            const banMessage = user.banExpiresAt && user.banExpiresAt > Date.now() 
                ? `Account is banned until ${user.banExpiresAt.toDateString()}`
                : 'Account is permanently banned';
                
            return res.status(403).json({
                success: false,
                message: banMessage,
                reason: user.banReason
            });
        }
        
        // Update last active time
        user.updateLastActive().catch(err => {
            console.warn('Failed to update last active time:', err);
        });
        
        // Attach user to request
        req.user = user;
        next();
        
    } catch (error) {
        console.error('Authentication error:', error);
        
        if (error.name === 'JsonWebTokenError') {
            return res.status(401).json({
                success: false,
                message: 'Invalid token'
            });
        }
        
        if (error.name === 'TokenExpiredError') {
            return res.status(401).json({
                success: false,
                message: 'Token expired'
            });
        }
        
        return res.status(500).json({
            success: false,
            message: 'Authentication failed'
        });
    }
};

// Optional authentication (doesn't fail if no token)
const optionalAuth = async (req, res, next) => {
    try {
        const authHeader = req.headers.authorization;
        const token = authHeader && authHeader.split(' ')[1];
        
        if (!token) {
            req.user = null;
            return next();
        }
        
        const decoded = jwt.verify(token, process.env.JWT_SECRET, {
            issuer: 'barber-brothers-community',
            audience: 'barber-brothers-users'
        });
        
        const user = await User.findById(decoded.id).select('-password');
        
        if (user && user.isActive && !user.isBanned) {
            req.user = user;
            user.updateLastActive().catch(err => {
                console.warn('Failed to update last active time:', err);
            });
        } else {
            req.user = null;
        }
        
        next();
        
    } catch (error) {
        // Don't fail on optional auth errors
        req.user = null;
        next();
    }
};

// Role-based authorization
const requireRole = (...roles) => {
    return (req, res, next) => {
        if (!req.user) {
            return res.status(401).json({
                success: false,
                message: 'Authentication required'
            });
        }
        
        if (!roles.includes(req.user.role)) {
            return res.status(403).json({
                success: false,
                message: 'Insufficient permissions',
                required: roles,
                current: req.user.role
            });
        }
        
        next();
    };
};

// Check if user owns resource or is admin
const requireOwnershipOrAdmin = (resourceField = 'author') => {
    return (req, res, next) => {
        if (!req.user) {
            return res.status(401).json({
                success: false,
                message: 'Authentication required'
            });
        }
        
        // Admin can access anything
        if (req.user.role === 'admin') {
            return next();
        }
        
        // Check ownership in the next middleware or controller
        req.requireOwnership = true;
        req.ownershipField = resourceField;
        next();
    };
};

// Verify email middleware
const requireVerifiedEmail = (req, res, next) => {
    if (!req.user) {
        return res.status(401).json({
            success: false,
            message: 'Authentication required'
        });
    }
    
    if (!req.user.isVerified) {
        return res.status(403).json({
            success: false,
            message: 'Email verification required',
            action: 'verify_email'
        });
    }
    
    next();
};

// Rate limiting for authenticated users
const authRateLimit = require('express-rate-limit')({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: (req) => {
        // Higher limits for verified users
        if (req.user && req.user.isVerified) {
            return req.user.role === 'admin' ? 1000 : 200;
        }
        return 50; // Lower limit for unverified users
    },
    message: {
        success: false,
        message: 'Too many requests, please try again later'
    },
    standardHeaders: true,
    legacyHeaders: false,
    keyGenerator: (req) => {
        return req.user ? req.user._id.toString() : req.ip;
    }
});

// Check user permissions for specific actions
const checkPermission = (action) => {
    return (req, res, next) => {
        if (!req.user) {
            return res.status(401).json({
                success: false,
                message: 'Authentication required'
            });
        }
        
        const permissions = {
            'create_post': ['user', 'barber', 'admin'],
            'moderate_content': ['admin'],
            'ban_user': ['admin'],
            'verify_user': ['admin'],
            'delete_any_post': ['admin'],
            'delete_any_comment': ['admin'],
            'view_reports': ['admin'],
            'manage_users': ['admin']
        };
        
        const allowedRoles = permissions[action];
        
        if (!allowedRoles || !allowedRoles.includes(req.user.role)) {
            return res.status(403).json({
                success: false,
                message: `Permission denied for action: ${action}`,
                required: allowedRoles,
                current: req.user.role
            });
        }
        
        next();
    };
};

// Middleware to check if user can interact with another user
const checkUserInteraction = async (req, res, next) => {
    try {
        if (!req.user) {
            return res.status(401).json({
                success: false,
                message: 'Authentication required'
            });
        }
        
        const targetUserId = req.params.userId || req.body.userId;
        
        if (!targetUserId) {
            return res.status(400).json({
                success: false,
                message: 'Target user ID required'
            });
        }
        
        // Can't interact with yourself for certain actions
        if (req.user._id.toString() === targetUserId.toString()) {
            const selfRestrictedActions = ['follow', 'unfollow', 'block'];
            const action = req.route.path.split('/').pop();
            
            if (selfRestrictedActions.includes(action)) {
                return res.status(400).json({
                    success: false,
                    message: `Cannot ${action} yourself`
                });
            }
        }
        
        // Check if target user exists and is active
        const targetUser = await User.findById(targetUserId);
        
        if (!targetUser || !targetUser.isActive) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }
        
        // Check if current user is blocked by target user
        if (targetUser.hasBlocked(req.user._id)) {
            return res.status(403).json({
                success: false,
                message: 'You are blocked by this user'
            });
        }
        
        req.targetUser = targetUser;
        next();
        
    } catch (error) {
        console.error('User interaction check error:', error);
        return res.status(500).json({
            success: false,
            message: 'Failed to verify user interaction'
        });
    }
};

// Middleware to log user actions for audit
const logUserAction = (action) => {
    return (req, res, next) => {
        if (req.user) {
            console.log(`User Action: ${req.user.username} performed ${action} at ${new Date().toISOString()}`);
            
            // You could save this to a separate audit log collection
            // const AuditLog = require('../models/AuditLog');
            // AuditLog.create({
            //     user: req.user._id,
            //     action,
            //     ip: req.ip,
            //     userAgent: req.get('User-Agent'),
            //     timestamp: new Date()
            // }).catch(err => console.error('Audit log error:', err));
        }
        next();
    };
};

module.exports = {
    authenticateToken,
    optionalAuth,
    requireRole,
    requireOwnershipOrAdmin,
    requireVerifiedEmail,
    authRateLimit,
    checkPermission,
    checkUserInteraction,
    logUserAction
};
