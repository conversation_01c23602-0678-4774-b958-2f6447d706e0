/**
 * File Upload Middleware
 * Multer configuration for handling image uploads with Cloudinary
 */

const multer = require('multer');
const cloudinary = require('cloudinary').v2;
const { CloudinaryStorage } = require('multer-storage-cloudinary');
const path = require('path');

// Configure Cloudinary
cloudinary.config({
    cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
    api_key: process.env.CLOUDINARY_API_KEY,
    api_secret: process.env.CLOUDINARY_API_SECRET
});

// File filter function
const fileFilter = (req, file, cb) => {
    // Check file type
    const allowedTypes = /jpeg|jpg|png|gif|webp/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);
    
    if (mimetype && extname) {
        return cb(null, true);
    } else {
        cb(new Error('Only image files (JPEG, JPG, PNG, GIF, WebP) are allowed'), false);
    }
};

// Cloudinary storage configuration for posts
const postStorage = new CloudinaryStorage({
    cloudinary: cloudinary,
    params: {
        folder: 'barber-brothers/posts',
        allowed_formats: ['jpg', 'jpeg', 'png', 'gif', 'webp'],
        transformation: [
            { width: 1200, height: 1200, crop: 'limit', quality: 'auto:good' },
            { fetch_format: 'auto' }
        ],
        public_id: (req, file) => {
            const timestamp = Date.now();
            const random = Math.random().toString(36).substring(7);
            return `post_${timestamp}_${random}`;
        }
    }
});

// Cloudinary storage configuration for avatars
const avatarStorage = new CloudinaryStorage({
    cloudinary: cloudinary,
    params: {
        folder: 'barber-brothers/avatars',
        allowed_formats: ['jpg', 'jpeg', 'png'],
        transformation: [
            { width: 400, height: 400, crop: 'fill', gravity: 'face', quality: 'auto:good' },
            { fetch_format: 'auto' }
        ],
        public_id: (req, file) => {
            return `avatar_${req.user._id}_${Date.now()}`;
        }
    }
});

// Cloudinary storage configuration for cover photos
const coverStorage = new CloudinaryStorage({
    cloudinary: cloudinary,
    params: {
        folder: 'barber-brothers/covers',
        allowed_formats: ['jpg', 'jpeg', 'png'],
        transformation: [
            { width: 1200, height: 400, crop: 'fill', quality: 'auto:good' },
            { fetch_format: 'auto' }
        ],
        public_id: (req, file) => {
            return `cover_${req.user._id}_${Date.now()}`;
        }
    }
});

// Memory storage for temporary processing
const memoryStorage = multer.memoryStorage();

// Upload configurations
const uploadPost = multer({
    storage: postStorage,
    fileFilter: fileFilter,
    limits: {
        fileSize: 10 * 1024 * 1024, // 10MB limit
        files: 4 // Maximum 4 images per post
    }
});

const uploadAvatar = multer({
    storage: avatarStorage,
    fileFilter: fileFilter,
    limits: {
        fileSize: 5 * 1024 * 1024, // 5MB limit
        files: 1 // Single file only
    }
});

const uploadCover = multer({
    storage: coverStorage,
    fileFilter: fileFilter,
    limits: {
        fileSize: 8 * 1024 * 1024, // 8MB limit
        files: 1 // Single file only
    }
});

const uploadMemory = multer({
    storage: memoryStorage,
    fileFilter: fileFilter,
    limits: {
        fileSize: 10 * 1024 * 1024, // 10MB limit
        files: 5 // Maximum 5 files
    }
});

// Error handling middleware for multer
const handleUploadError = (error, req, res, next) => {
    if (error instanceof multer.MulterError) {
        switch (error.code) {
            case 'LIMIT_FILE_SIZE':
                return res.status(400).json({
                    success: false,
                    message: 'File too large. Maximum size is 10MB per file.',
                    code: 'FILE_TOO_LARGE'
                });
            case 'LIMIT_FILE_COUNT':
                return res.status(400).json({
                    success: false,
                    message: 'Too many files. Maximum 4 images per post.',
                    code: 'TOO_MANY_FILES'
                });
            case 'LIMIT_UNEXPECTED_FILE':
                return res.status(400).json({
                    success: false,
                    message: 'Unexpected file field.',
                    code: 'UNEXPECTED_FILE'
                });
            default:
                return res.status(400).json({
                    success: false,
                    message: 'File upload error.',
                    code: 'UPLOAD_ERROR'
                });
        }
    }
    
    if (error.message.includes('Only image files')) {
        return res.status(400).json({
            success: false,
            message: 'Only image files (JPEG, JPG, PNG, GIF, WebP) are allowed.',
            code: 'INVALID_FILE_TYPE'
        });
    }
    
    console.error('Upload error:', error);
    return res.status(500).json({
        success: false,
        message: 'File upload failed.',
        code: 'UPLOAD_FAILED'
    });
};

// Middleware to validate uploaded files
const validateUploadedFiles = (req, res, next) => {
    if (!req.files && !req.file) {
        return next();
    }
    
    const files = req.files || [req.file];
    
    // Validate each file
    for (const file of files) {
        if (!file) continue;
        
        // Check file size (additional check)
        if (file.size > 10 * 1024 * 1024) {
            return res.status(400).json({
                success: false,
                message: `File ${file.originalname} is too large. Maximum size is 10MB.`,
                code: 'FILE_TOO_LARGE'
            });
        }
        
        // Check file type
        const allowedMimes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
        if (!allowedMimes.includes(file.mimetype)) {
            return res.status(400).json({
                success: false,
                message: `File ${file.originalname} has invalid type. Only images are allowed.`,
                code: 'INVALID_FILE_TYPE'
            });
        }
    }
    
    next();
};

// Middleware to process uploaded images
const processUploadedImages = async (req, res, next) => {
    try {
        if (!req.files && !req.file) {
            return next();
        }
        
        const files = req.files || [req.file];
        const processedImages = [];
        
        for (const file of files) {
            if (!file) continue;
            
            const imageData = {
                url: file.path,
                public_id: file.filename,
                width: file.width || null,
                height: file.height || null,
                size: file.size,
                format: file.format || path.extname(file.originalname).substring(1),
                alt: file.originalname
            };
            
            processedImages.push(imageData);
        }
        
        req.processedImages = processedImages;
        next();
        
    } catch (error) {
        console.error('Image processing error:', error);
        return res.status(500).json({
            success: false,
            message: 'Failed to process uploaded images.',
            code: 'PROCESSING_FAILED'
        });
    }
};

// Middleware to clean up uploaded files on error
const cleanupOnError = (req, res, next) => {
    const originalSend = res.send;
    
    res.send = function(data) {
        // If response is an error and we have uploaded files, clean them up
        if (res.statusCode >= 400 && req.processedImages) {
            req.processedImages.forEach(image => {
                if (image.public_id) {
                    cloudinary.uploader.destroy(image.public_id).catch(err => {
                        console.error('Failed to cleanup image:', err);
                    });
                }
            });
        }
        
        originalSend.call(this, data);
    };
    
    next();
};

// Utility function to delete image from Cloudinary
const deleteImage = async (publicId) => {
    try {
        const result = await cloudinary.uploader.destroy(publicId);
        return result;
    } catch (error) {
        console.error('Error deleting image from Cloudinary:', error);
        throw error;
    }
};

// Utility function to get image info
const getImageInfo = async (publicId) => {
    try {
        const result = await cloudinary.api.resource(publicId);
        return result;
    } catch (error) {
        console.error('Error getting image info:', error);
        throw error;
    }
};

// Middleware factory for different upload types
const createUploadMiddleware = (type) => {
    switch (type) {
        case 'post':
            return [
                uploadPost.array('images', 4),
                handleUploadError,
                validateUploadedFiles,
                processUploadedImages,
                cleanupOnError
            ];
        case 'avatar':
            return [
                uploadAvatar.single('avatar'),
                handleUploadError,
                validateUploadedFiles,
                processUploadedImages,
                cleanupOnError
            ];
        case 'cover':
            return [
                uploadCover.single('cover'),
                handleUploadError,
                validateUploadedFiles,
                processUploadedImages,
                cleanupOnError
            ];
        default:
            return [
                uploadMemory.array('files', 5),
                handleUploadError,
                validateUploadedFiles,
                cleanupOnError
            ];
    }
};

module.exports = {
    uploadPost,
    uploadAvatar,
    uploadCover,
    uploadMemory,
    handleUploadError,
    validateUploadedFiles,
    processUploadedImages,
    cleanupOnError,
    createUploadMiddleware,
    deleteImage,
    getImageInfo,
    cloudinary
};
