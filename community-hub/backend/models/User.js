/**
 * User Model
 * MongoDB schema for user data with comprehensive validation
 */

const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const userSchema = new mongoose.Schema({
    // Basic Information
    googleId: {
        type: String,
        unique: true,
        sparse: true,
        index: true
    },
    email: {
        type: String,
        required: [true, 'Email is required'],
        unique: true,
        lowercase: true,
        trim: true,
        match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
    },
    username: {
        type: String,
        required: [true, 'Username is required'],
        unique: true,
        trim: true,
        minlength: [3, 'Username must be at least 3 characters'],
        maxlength: [30, 'Username cannot exceed 30 characters'],
        match: [/^[a-zA-Z0-9_]+$/, 'Username can only contain letters, numbers, and underscores']
    },
    password: {
        type: String,
        minlength: [6, 'Password must be at least 6 characters'],
        select: false // Don't include password in queries by default
    },
    
    // Profile Information
    displayName: {
        type: String,
        required: [true, 'Display name is required'],
        trim: true,
        maxlength: [50, 'Display name cannot exceed 50 characters']
    },
    firstName: {
        type: String,
        trim: true,
        maxlength: [30, 'First name cannot exceed 30 characters']
    },
    lastName: {
        type: String,
        trim: true,
        maxlength: [30, 'Last name cannot exceed 30 characters']
    },
    bio: {
        type: String,
        maxlength: [500, 'Bio cannot exceed 500 characters'],
        trim: true
    },
    location: {
        type: String,
        maxlength: [100, 'Location cannot exceed 100 characters'],
        trim: true
    },
    website: {
        type: String,
        maxlength: [200, 'Website URL cannot exceed 200 characters'],
        trim: true,
        match: [/^https?:\/\/.+/, 'Please enter a valid URL']
    },
    
    // Avatar and Media
    avatar: {
        type: String,
        default: null
    },
    coverPhoto: {
        type: String,
        default: null
    },
    
    // Authentication & Security
    authProvider: {
        type: String,
        enum: ['local', 'google'],
        default: 'local'
    },
    isVerified: {
        type: Boolean,
        default: false
    },
    emailVerificationToken: {
        type: String,
        select: false
    },
    emailVerificationExpires: {
        type: Date,
        select: false
    },
    passwordResetToken: {
        type: String,
        select: false
    },
    passwordResetExpires: {
        type: Date,
        select: false
    },
    
    // Role and Permissions
    role: {
        type: String,
        enum: ['user', 'barber', 'admin'],
        default: 'user'
    },
    isActive: {
        type: Boolean,
        default: true
    },
    isBanned: {
        type: Boolean,
        default: false
    },
    banReason: {
        type: String,
        maxlength: [200, 'Ban reason cannot exceed 200 characters']
    },
    banExpiresAt: {
        type: Date
    },
    
    // Social Features
    followers: [{
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
    }],
    following: [{
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
    }],
    blockedUsers: [{
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
    }],
    
    // Statistics
    postsCount: {
        type: Number,
        default: 0,
        min: 0
    },
    followersCount: {
        type: Number,
        default: 0,
        min: 0
    },
    followingCount: {
        type: Number,
        default: 0,
        min: 0
    },
    likesReceived: {
        type: Number,
        default: 0,
        min: 0
    },
    
    // Preferences
    preferences: {
        notifications: {
            type: Boolean,
            default: true
        },
        emailUpdates: {
            type: Boolean,
            default: true
        },
        privacy: {
            type: String,
            enum: ['public', 'private'],
            default: 'public'
        },
        theme: {
            type: String,
            enum: ['dark', 'light'],
            default: 'dark'
        },
        language: {
            type: String,
            default: 'en'
        }
    },
    
    // Activity Tracking
    lastLoginAt: {
        type: Date,
        default: Date.now
    },
    lastActiveAt: {
        type: Date,
        default: Date.now
    },
    isOnline: {
        type: Boolean,
        default: false
    },
    loginCount: {
        type: Number,
        default: 0
    },
    
    // Metadata
    createdAt: {
        type: Date,
        default: Date.now
    },
    updatedAt: {
        type: Date,
        default: Date.now
    }
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});

// Indexes for performance
userSchema.index({ email: 1 });
userSchema.index({ username: 1 });
userSchema.index({ googleId: 1 });
userSchema.index({ role: 1 });
userSchema.index({ isActive: 1 });
userSchema.index({ createdAt: -1 });
userSchema.index({ lastActiveAt: -1 });
userSchema.index({ followersCount: -1 });
userSchema.index({ postsCount: -1 });

// Virtual for full name
userSchema.virtual('fullName').get(function() {
    if (this.firstName && this.lastName) {
        return `${this.firstName} ${this.lastName}`;
    }
    return this.displayName;
});

// Virtual for follower/following ratio
userSchema.virtual('engagementRatio').get(function() {
    if (this.followingCount === 0) return this.followersCount;
    return Math.round((this.followersCount / this.followingCount) * 100) / 100;
});

// Pre-save middleware
userSchema.pre('save', async function(next) {
    // Update the updatedAt field
    this.updatedAt = Date.now();
    
    // Hash password if it's modified and not from OAuth
    if (this.isModified('password') && this.password) {
        try {
            const salt = await bcrypt.genSalt(12);
            this.password = await bcrypt.hash(this.password, salt);
        } catch (error) {
            return next(error);
        }
    }
    
    // Update follower/following counts
    if (this.isModified('followers')) {
        this.followersCount = this.followers.length;
    }
    
    if (this.isModified('following')) {
        this.followingCount = this.following.length;
    }
    
    next();
});

// Instance methods
userSchema.methods.comparePassword = async function(candidatePassword) {
    if (!this.password) return false;
    return await bcrypt.compare(candidatePassword, this.password);
};

userSchema.methods.generateUsername = function() {
    const baseUsername = this.email.split('@')[0];
    const randomSuffix = Math.floor(Math.random() * 1000);
    return `${baseUsername}${randomSuffix}`;
};

userSchema.methods.isFollowing = function(userId) {
    return this.following.includes(userId);
};

userSchema.methods.isFollowedBy = function(userId) {
    return this.followers.includes(userId);
};

userSchema.methods.hasBlocked = function(userId) {
    return this.blockedUsers.includes(userId);
};

userSchema.methods.updateLastActive = function() {
    this.lastActiveAt = Date.now();
    this.isOnline = true;
    return this.save();
};

userSchema.methods.setOffline = function() {
    this.isOnline = false;
    return this.save();
};

// Static methods
userSchema.statics.findByEmail = function(email) {
    return this.findOne({ email: email.toLowerCase() });
};

userSchema.statics.findByUsername = function(username) {
    return this.findOne({ username: username.toLowerCase() });
};

userSchema.statics.findActiveUsers = function() {
    return this.find({ isActive: true, isBanned: false });
};

userSchema.statics.findVerifiedUsers = function() {
    return this.find({ isVerified: true, isActive: true });
};

userSchema.statics.searchUsers = function(query, limit = 20) {
    const searchRegex = new RegExp(query, 'i');
    return this.find({
        $or: [
            { displayName: searchRegex },
            { username: searchRegex },
            { email: searchRegex }
        ],
        isActive: true,
        isBanned: false
    }).limit(limit);
};

module.exports = mongoose.model('User', userSchema);
