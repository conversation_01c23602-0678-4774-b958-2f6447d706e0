/**
 * Comment Model
 * MongoDB schema for post comments with nested replies support
 */

const mongoose = require('mongoose');

const commentSchema = new mongoose.Schema({
    // Reference to the post
    post: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Post',
        required: [true, 'Comment must belong to a post'],
        index: true
    },
    
    // Author of the comment
    author: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: [true, 'Comment must have an author'],
        index: true
    },
    
    // Comment content
    content: {
        type: String,
        required: [true, 'Comment content is required'],
        trim: true,
        maxlength: [1000, 'Comment cannot exceed 1000 characters']
    },
    
    // Nested comments (replies)
    parentComment: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Comment',
        default: null,
        index: true
    },
    
    replies: [{
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Comment'
    }],
    
    repliesCount: {
        type: Number,
        default: 0,
        min: 0
    },
    
    // Engagement
    likes: [{
        user: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'User',
            required: true
        },
        createdAt: {
            type: Date,
            default: Date.now
        }
    }],
    
    likesCount: {
        type: Number,
        default: 0,
        min: 0
    },
    
    // Mentions in comment
    mentions: [{
        user: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'User'
        },
        username: String,
        position: Number
    }],
    
    // Content moderation
    isReported: {
        type: Boolean,
        default: false
    },
    
    reportCount: {
        type: Number,
        default: 0,
        min: 0
    },
    
    reports: [{
        reporter: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'User'
        },
        reason: {
            type: String,
            enum: ['spam', 'harassment', 'inappropriate', 'other']
        },
        description: String,
        createdAt: {
            type: Date,
            default: Date.now
        }
    }],
    
    moderationStatus: {
        type: String,
        enum: ['pending', 'approved', 'rejected', 'flagged'],
        default: 'approved'
    },
    
    moderatedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
    },
    
    moderatedAt: Date,
    
    // Comment status
    isActive: {
        type: Boolean,
        default: true
    },
    
    isDeleted: {
        type: Boolean,
        default: false
    },
    
    deletedAt: Date,
    
    deletedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
    },
    
    // Edit history
    isEdited: {
        type: Boolean,
        default: false
    },
    
    editHistory: [{
        content: String,
        editedAt: {
            type: Date,
            default: Date.now
        }
    }],
    
    lastEditedAt: Date,
    
    // Timestamps
    createdAt: {
        type: Date,
        default: Date.now,
        index: true
    },
    
    updatedAt: {
        type: Date,
        default: Date.now
    }
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});

// Indexes for performance
commentSchema.index({ post: 1, createdAt: -1 });
commentSchema.index({ author: 1, createdAt: -1 });
commentSchema.index({ parentComment: 1, createdAt: 1 });
commentSchema.index({ isActive: 1, isDeleted: 1 });
commentSchema.index({ moderationStatus: 1 });
commentSchema.index({ likesCount: -1 });

// Text index for search
commentSchema.index({ content: 'text' });

// Virtual for comment depth (how nested it is)
commentSchema.virtual('depth').get(function() {
    let depth = 0;
    let current = this;
    
    while (current.parentComment) {
        depth++;
        current = current.parentComment;
        if (depth > 10) break; // Prevent infinite loops
    }
    
    return depth;
});

// Virtual for time since creation
commentSchema.virtual('timeAgo').get(function() {
    const now = new Date();
    const diff = now - this.createdAt;
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);
    
    if (days > 0) return `${days}d`;
    if (hours > 0) return `${hours}h`;
    if (minutes > 0) return `${minutes}m`;
    return 'now';
});

// Pre-save middleware
commentSchema.pre('save', function(next) {
    // Update the updatedAt field
    this.updatedAt = Date.now();
    
    // Update counts
    this.likesCount = this.likes.length;
    this.repliesCount = this.replies.length;
    
    // Extract mentions from content
    this.extractMentions();
    
    next();
});

// Post-save middleware to update parent comment and post counts
commentSchema.post('save', async function(doc) {
    try {
        // Update parent comment replies count
        if (doc.parentComment) {
            await mongoose.model('Comment').findByIdAndUpdate(
                doc.parentComment,
                { $addToSet: { replies: doc._id } }
            );
        }
        
        // Update post comments count
        const Post = mongoose.model('Post');
        const commentsCount = await mongoose.model('Comment').countDocuments({
            post: doc.post,
            isActive: true,
            isDeleted: false
        });
        
        await Post.findByIdAndUpdate(doc.post, { commentsCount });
        
    } catch (error) {
        console.error('Error updating comment counts:', error);
    }
});

// Post-remove middleware to update counts
commentSchema.post('remove', async function(doc) {
    try {
        // Update parent comment replies count
        if (doc.parentComment) {
            await mongoose.model('Comment').findByIdAndUpdate(
                doc.parentComment,
                { $pull: { replies: doc._id } }
            );
        }
        
        // Update post comments count
        const Post = mongoose.model('Post');
        const commentsCount = await mongoose.model('Comment').countDocuments({
            post: doc.post,
            isActive: true,
            isDeleted: false
        });
        
        await Post.findByIdAndUpdate(doc.post, { commentsCount });
        
    } catch (error) {
        console.error('Error updating comment counts after removal:', error);
    }
});

// Instance methods
commentSchema.methods.extractMentions = function() {
    const mentionRegex = /@(\w+)/g;
    const mentions = [];
    let match;
    
    while ((match = mentionRegex.exec(this.content)) !== null) {
        mentions.push({
            username: match[1].toLowerCase(),
            position: match.index
        });
    }
    
    this.mentions = mentions;
};

commentSchema.methods.addLike = function(userId) {
    const existingLike = this.likes.find(like => like.user.toString() === userId.toString());
    
    if (!existingLike) {
        this.likes.push({ user: userId });
        this.likesCount = this.likes.length;
        return true; // Like added
    }
    
    return false; // Already liked
};

commentSchema.methods.removeLike = function(userId) {
    const likeIndex = this.likes.findIndex(like => like.user.toString() === userId.toString());
    
    if (likeIndex > -1) {
        this.likes.splice(likeIndex, 1);
        this.likesCount = this.likes.length;
        return true; // Like removed
    }
    
    return false; // Not liked
};

commentSchema.methods.isLikedBy = function(userId) {
    return this.likes.some(like => like.user.toString() === userId.toString());
};

commentSchema.methods.addReport = function(reportData) {
    this.reports.push(reportData);
    this.reportCount = this.reports.length;
    this.isReported = true;
    
    // Auto-flag if too many reports
    if (this.reportCount >= 3) {
        this.moderationStatus = 'flagged';
    }
};

commentSchema.methods.softDelete = function(deletedBy) {
    this.isDeleted = true;
    this.isActive = false;
    this.deletedAt = Date.now();
    this.deletedBy = deletedBy;
    return this.save();
};

commentSchema.methods.addEdit = function(newContent, reason = '') {
    // Store current content in edit history
    this.editHistory.push({
        content: this.content,
        editedAt: Date.now()
    });
    
    // Update content
    this.content = newContent;
    this.isEdited = true;
    this.lastEditedAt = Date.now();
    
    return this.save();
};

// Static methods
commentSchema.statics.findByPost = function(postId, options = {}) {
    const {
        limit = 20,
        skip = 0,
        sortBy = 'createdAt',
        sortOrder = -1,
        includeReplies = false
    } = options;
    
    const query = {
        post: postId,
        isActive: true,
        isDeleted: false,
        moderationStatus: 'approved'
    };
    
    // Only get top-level comments if not including replies
    if (!includeReplies) {
        query.parentComment = null;
    }
    
    return this.find(query)
        .populate('author', 'username displayName avatar role isVerified')
        .populate({
            path: 'replies',
            populate: {
                path: 'author',
                select: 'username displayName avatar role isVerified'
            },
            match: { isActive: true, isDeleted: false }
        })
        .sort({ [sortBy]: sortOrder })
        .limit(limit)
        .skip(skip);
};

commentSchema.statics.findReplies = function(parentCommentId, limit = 10) {
    return this.find({
        parentComment: parentCommentId,
        isActive: true,
        isDeleted: false,
        moderationStatus: 'approved'
    })
    .populate('author', 'username displayName avatar role isVerified')
    .sort({ createdAt: 1 })
    .limit(limit);
};

commentSchema.statics.findByAuthor = function(authorId, limit = 20, skip = 0) {
    return this.find({
        author: authorId,
        isActive: true,
        isDeleted: false
    })
    .populate('post', 'content author createdAt')
    .sort({ createdAt: -1 })
    .limit(limit)
    .skip(skip);
};

commentSchema.statics.searchComments = function(query, limit = 20) {
    return this.find({
        $text: { $search: query },
        isActive: true,
        isDeleted: false,
        moderationStatus: 'approved'
    })
    .populate('author', 'username displayName avatar role isVerified')
    .populate('post', 'content author')
    .sort({ score: { $meta: 'textScore' } })
    .limit(limit);
};

module.exports = mongoose.model('Comment', commentSchema);
