/**
 * Post Model
 * MongoDB schema for social media posts with comprehensive features
 */

const mongoose = require('mongoose');

const postSchema = new mongoose.Schema({
    // Author Information
    author: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: [true, 'Post must have an author'],
        index: true
    },
    
    // Content
    content: {
        type: String,
        required: [true, 'Post content is required'],
        trim: true,
        maxlength: [2000, 'Post content cannot exceed 2000 characters']
    },
    
    // Media Attachments
    images: [{
        url: {
            type: String,
            required: true
        },
        alt: {
            type: String,
            maxlength: [200, 'Alt text cannot exceed 200 characters']
        },
        width: Number,
        height: Number,
        size: Number, // File size in bytes
        format: {
            type: String,
            enum: ['jpg', 'jpeg', 'png', 'gif', 'webp']
        }
    }],
    
    // Engagement
    likes: [{
        user: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'User',
            required: true
        },
        createdAt: {
            type: Date,
            default: Date.now
        }
    }],
    
    likesCount: {
        type: Number,
        default: 0,
        min: 0
    },
    
    commentsCount: {
        type: Number,
        default: 0,
        min: 0
    },
    
    sharesCount: {
        type: Number,
        default: 0,
        min: 0
    },
    
    viewsCount: {
        type: Number,
        default: 0,
        min: 0
    },
    
    // Social Features
    mentions: [{
        user: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'User'
        },
        username: String,
        position: Number // Position in the content where mention occurs
    }],
    
    hashtags: [{
        tag: {
            type: String,
            lowercase: true,
            trim: true
        },
        position: Number
    }],
    
    // Post Settings
    visibility: {
        type: String,
        enum: ['public', 'followers', 'private'],
        default: 'public'
    },
    
    allowComments: {
        type: Boolean,
        default: true
    },
    
    allowShares: {
        type: Boolean,
        default: true
    },
    
    // Location
    location: {
        name: String,
        coordinates: {
            type: [Number], // [longitude, latitude]
            index: '2dsphere'
        },
        address: String,
        city: String,
        country: String
    },
    
    // Content Moderation
    isReported: {
        type: Boolean,
        default: false
    },
    
    reportCount: {
        type: Number,
        default: 0,
        min: 0
    },
    
    reports: [{
        reporter: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'User'
        },
        reason: {
            type: String,
            enum: ['spam', 'harassment', 'inappropriate', 'copyright', 'other']
        },
        description: String,
        createdAt: {
            type: Date,
            default: Date.now
        }
    }],
    
    moderationStatus: {
        type: String,
        enum: ['pending', 'approved', 'rejected', 'flagged'],
        default: 'approved'
    },
    
    moderatedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
    },
    
    moderatedAt: Date,
    
    // Post Status
    isActive: {
        type: Boolean,
        default: true
    },
    
    isDeleted: {
        type: Boolean,
        default: false
    },
    
    deletedAt: Date,
    
    deletedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
    },
    
    // Edit History
    isEdited: {
        type: Boolean,
        default: false
    },
    
    editHistory: [{
        content: String,
        editedAt: {
            type: Date,
            default: Date.now
        },
        reason: String
    }],
    
    lastEditedAt: Date,
    
    // Analytics
    engagement: {
        score: {
            type: Number,
            default: 0
        },
        lastCalculated: {
            type: Date,
            default: Date.now
        }
    },
    
    // Timestamps
    createdAt: {
        type: Date,
        default: Date.now,
        index: true
    },
    
    updatedAt: {
        type: Date,
        default: Date.now
    }
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});

// Indexes for performance
postSchema.index({ author: 1, createdAt: -1 });
postSchema.index({ createdAt: -1 });
postSchema.index({ visibility: 1, isActive: 1 });
postSchema.index({ 'hashtags.tag': 1 });
postSchema.index({ likesCount: -1 });
postSchema.index({ commentsCount: -1 });
postSchema.index({ 'engagement.score': -1 });
postSchema.index({ moderationStatus: 1 });
postSchema.index({ isReported: 1 });

// Text index for search
postSchema.index({ 
    content: 'text',
    'hashtags.tag': 'text'
});

// Virtual for engagement rate
postSchema.virtual('engagementRate').get(function() {
    if (this.viewsCount === 0) return 0;
    const totalEngagement = this.likesCount + this.commentsCount + this.sharesCount;
    return Math.round((totalEngagement / this.viewsCount) * 100) / 100;
});

// Virtual for time since creation
postSchema.virtual('timeAgo').get(function() {
    const now = new Date();
    const diff = now - this.createdAt;
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);
    
    if (days > 0) return `${days}d`;
    if (hours > 0) return `${hours}h`;
    if (minutes > 0) return `${minutes}m`;
    return 'now';
});

// Pre-save middleware
postSchema.pre('save', function(next) {
    // Update the updatedAt field
    this.updatedAt = Date.now();
    
    // Update counts
    this.likesCount = this.likes.length;
    
    // Calculate engagement score
    this.calculateEngagementScore();
    
    // Extract hashtags and mentions from content
    this.extractHashtagsAndMentions();
    
    next();
});

// Instance methods
postSchema.methods.calculateEngagementScore = function() {
    const ageInHours = (Date.now() - this.createdAt) / (1000 * 60 * 60);
    const decayFactor = Math.exp(-ageInHours / 24); // Decay over 24 hours
    
    const score = (
        (this.likesCount * 1) +
        (this.commentsCount * 2) +
        (this.sharesCount * 3) +
        (this.viewsCount * 0.1)
    ) * decayFactor;
    
    this.engagement.score = Math.round(score * 100) / 100;
    this.engagement.lastCalculated = Date.now();
};

postSchema.methods.extractHashtagsAndMentions = function() {
    // Extract hashtags
    const hashtagRegex = /#(\w+)/g;
    const hashtags = [];
    let match;
    
    while ((match = hashtagRegex.exec(this.content)) !== null) {
        hashtags.push({
            tag: match[1].toLowerCase(),
            position: match.index
        });
    }
    
    this.hashtags = hashtags;
    
    // Extract mentions (would need user lookup for validation)
    const mentionRegex = /@(\w+)/g;
    const mentions = [];
    
    while ((match = mentionRegex.exec(this.content)) !== null) {
        mentions.push({
            username: match[1].toLowerCase(),
            position: match.index
        });
    }
    
    // Note: User lookup for mentions would be done in the controller
    this.mentions = mentions;
};

postSchema.methods.addLike = function(userId) {
    const existingLike = this.likes.find(like => like.user.toString() === userId.toString());
    
    if (!existingLike) {
        this.likes.push({ user: userId });
        this.likesCount = this.likes.length;
        return true; // Like added
    }
    
    return false; // Already liked
};

postSchema.methods.removeLike = function(userId) {
    const likeIndex = this.likes.findIndex(like => like.user.toString() === userId.toString());
    
    if (likeIndex > -1) {
        this.likes.splice(likeIndex, 1);
        this.likesCount = this.likes.length;
        return true; // Like removed
    }
    
    return false; // Not liked
};

postSchema.methods.isLikedBy = function(userId) {
    return this.likes.some(like => like.user.toString() === userId.toString());
};

postSchema.methods.addView = function() {
    this.viewsCount += 1;
    return this.save();
};

postSchema.methods.addReport = function(reportData) {
    this.reports.push(reportData);
    this.reportCount = this.reports.length;
    this.isReported = true;
    
    // Auto-flag if too many reports
    if (this.reportCount >= 5) {
        this.moderationStatus = 'flagged';
    }
};

postSchema.methods.softDelete = function(deletedBy) {
    this.isDeleted = true;
    this.isActive = false;
    this.deletedAt = Date.now();
    this.deletedBy = deletedBy;
    return this.save();
};

// Static methods
postSchema.statics.findPublicPosts = function(limit = 20, skip = 0) {
    return this.find({
        visibility: 'public',
        isActive: true,
        isDeleted: false,
        moderationStatus: 'approved'
    })
    .populate('author', 'username displayName avatar role isVerified')
    .sort({ createdAt: -1 })
    .limit(limit)
    .skip(skip);
};

postSchema.statics.findByHashtag = function(hashtag, limit = 20) {
    return this.find({
        'hashtags.tag': hashtag.toLowerCase(),
        visibility: 'public',
        isActive: true,
        isDeleted: false
    })
    .populate('author', 'username displayName avatar role isVerified')
    .sort({ createdAt: -1 })
    .limit(limit);
};

postSchema.statics.findTrending = function(limit = 20) {
    return this.find({
        visibility: 'public',
        isActive: true,
        isDeleted: false,
        moderationStatus: 'approved'
    })
    .populate('author', 'username displayName avatar role isVerified')
    .sort({ 'engagement.score': -1, createdAt: -1 })
    .limit(limit);
};

postSchema.statics.searchPosts = function(query, limit = 20) {
    return this.find({
        $text: { $search: query },
        visibility: 'public',
        isActive: true,
        isDeleted: false
    })
    .populate('author', 'username displayName avatar role isVerified')
    .sort({ score: { $meta: 'textScore' } })
    .limit(limit);
};

module.exports = mongoose.model('Post', postSchema);
