/**
 * Notification Model
 * MongoDB schema for user notifications with comprehensive types and settings
 */

const mongoose = require('mongoose');

const notificationSchema = new mongoose.Schema({
    // Recipient of the notification
    recipient: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: [true, 'Notification must have a recipient'],
        index: true
    },
    
    // Sender of the notification (who triggered it)
    sender: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: [true, 'Notification must have a sender']
    },
    
    // Notification type
    type: {
        type: String,
        enum: [
            'like_post',
            'like_comment',
            'comment_post',
            'reply_comment',
            'follow',
            'unfollow',
            'mention_post',
            'mention_comment',
            'post_approved',
            'post_rejected',
            'welcome',
            'system'
        ],
        required: [true, 'Notification type is required'],
        index: true
    },
    
    // Notification title
    title: {
        type: String,
        required: [true, 'Notification title is required'],
        maxlength: [100, 'Title cannot exceed 100 characters']
    },
    
    // Notification message
    message: {
        type: String,
        required: [true, 'Notification message is required'],
        maxlength: [500, 'Message cannot exceed 500 characters']
    },
    
    // Related entities
    relatedPost: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Post',
        default: null
    },
    
    relatedComment: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Comment',
        default: null
    },
    
    relatedUser: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        default: null
    },
    
    // Action URL for navigation
    actionUrl: {
        type: String,
        maxlength: [200, 'Action URL cannot exceed 200 characters']
    },
    
    // Notification status
    isRead: {
        type: Boolean,
        default: false,
        index: true
    },
    
    readAt: {
        type: Date,
        default: null
    },
    
    // Delivery status
    isDelivered: {
        type: Boolean,
        default: false
    },
    
    deliveredAt: {
        type: Date,
        default: null
    },
    
    // Email notification
    emailSent: {
        type: Boolean,
        default: false
    },
    
    emailSentAt: {
        type: Date,
        default: null
    },
    
    // Push notification
    pushSent: {
        type: Boolean,
        default: false
    },
    
    pushSentAt: {
        type: Date,
        default: null
    },
    
    // Priority level
    priority: {
        type: String,
        enum: ['low', 'normal', 'high', 'urgent'],
        default: 'normal'
    },
    
    // Expiration
    expiresAt: {
        type: Date,
        default: null,
        index: { expireAfterSeconds: 0 }
    },
    
    // Metadata for additional data
    metadata: {
        type: mongoose.Schema.Types.Mixed,
        default: {}
    },
    
    // Timestamps
    createdAt: {
        type: Date,
        default: Date.now,
        index: true
    },
    
    updatedAt: {
        type: Date,
        default: Date.now
    }
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});

// Compound indexes for performance
notificationSchema.index({ recipient: 1, createdAt: -1 });
notificationSchema.index({ recipient: 1, isRead: 1, createdAt: -1 });
notificationSchema.index({ recipient: 1, type: 1, createdAt: -1 });
notificationSchema.index({ sender: 1, createdAt: -1 });
notificationSchema.index({ type: 1, createdAt: -1 });
notificationSchema.index({ priority: 1, createdAt: -1 });

// Virtual for time since creation
notificationSchema.virtual('timeAgo').get(function() {
    const now = new Date();
    const diff = now - this.createdAt;
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);
    const weeks = Math.floor(days / 7);
    
    if (weeks > 0) return `${weeks}w`;
    if (days > 0) return `${days}d`;
    if (hours > 0) return `${hours}h`;
    if (minutes > 0) return `${minutes}m`;
    return 'now';
});

// Virtual for notification icon based on type
notificationSchema.virtual('icon').get(function() {
    const iconMap = {
        'like_post': 'heart',
        'like_comment': 'heart',
        'comment_post': 'comment',
        'reply_comment': 'reply',
        'follow': 'user-plus',
        'unfollow': 'user-minus',
        'mention_post': 'at',
        'mention_comment': 'at',
        'post_approved': 'check-circle',
        'post_rejected': 'x-circle',
        'welcome': 'star',
        'system': 'info'
    };
    
    return iconMap[this.type] || 'bell';
});

// Pre-save middleware
notificationSchema.pre('save', function(next) {
    this.updatedAt = Date.now();
    
    // Set default expiration (30 days from creation)
    if (!this.expiresAt) {
        this.expiresAt = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);
    }
    
    // Generate action URL if not provided
    if (!this.actionUrl) {
        this.generateActionUrl();
    }
    
    next();
});

// Instance methods
notificationSchema.methods.markAsRead = function() {
    if (!this.isRead) {
        this.isRead = true;
        this.readAt = Date.now();
        return this.save();
    }
    return Promise.resolve(this);
};

notificationSchema.methods.markAsDelivered = function() {
    if (!this.isDelivered) {
        this.isDelivered = true;
        this.deliveredAt = Date.now();
        return this.save();
    }
    return Promise.resolve(this);
};

notificationSchema.methods.markEmailSent = function() {
    this.emailSent = true;
    this.emailSentAt = Date.now();
    return this.save();
};

notificationSchema.methods.markPushSent = function() {
    this.pushSent = true;
    this.pushSentAt = Date.now();
    return this.save();
};

notificationSchema.methods.generateActionUrl = function() {
    switch (this.type) {
        case 'like_post':
        case 'comment_post':
        case 'mention_post':
            if (this.relatedPost) {
                this.actionUrl = `/posts/${this.relatedPost}`;
            }
            break;
            
        case 'like_comment':
        case 'reply_comment':
        case 'mention_comment':
            if (this.relatedPost && this.relatedComment) {
                this.actionUrl = `/posts/${this.relatedPost}#comment-${this.relatedComment}`;
            }
            break;
            
        case 'follow':
        case 'unfollow':
            if (this.sender) {
                this.actionUrl = `/users/${this.sender}`;
            }
            break;
            
        default:
            this.actionUrl = '/notifications';
    }
};

// Static methods
notificationSchema.statics.createNotification = async function(data) {
    try {
        // Check if recipient has notifications enabled
        const User = mongoose.model('User');
        const recipient = await User.findById(data.recipient);
        
        if (!recipient || !recipient.preferences.notifications) {
            return null;
        }
        
        // Prevent self-notifications
        if (data.recipient.toString() === data.sender.toString()) {
            return null;
        }
        
        // Check for duplicate notifications (within last hour)
        const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
        const existingNotification = await this.findOne({
            recipient: data.recipient,
            sender: data.sender,
            type: data.type,
            relatedPost: data.relatedPost,
            relatedComment: data.relatedComment,
            createdAt: { $gte: oneHourAgo }
        });
        
        if (existingNotification) {
            return existingNotification;
        }
        
        // Create notification
        const notification = new this(data);
        await notification.save();
        
        return notification;
        
    } catch (error) {
        console.error('Error creating notification:', error);
        throw error;
    }
};

notificationSchema.statics.findByRecipient = function(recipientId, options = {}) {
    const {
        limit = 20,
        skip = 0,
        unreadOnly = false,
        type = null
    } = options;
    
    const query = { recipient: recipientId };
    
    if (unreadOnly) {
        query.isRead = false;
    }
    
    if (type) {
        query.type = type;
    }
    
    return this.find(query)
        .populate('sender', 'username displayName avatar role isVerified')
        .populate('relatedPost', 'content images')
        .populate('relatedComment', 'content')
        .sort({ createdAt: -1 })
        .limit(limit)
        .skip(skip);
};

notificationSchema.statics.getUnreadCount = function(recipientId) {
    return this.countDocuments({
        recipient: recipientId,
        isRead: false
    });
};

notificationSchema.statics.markAllAsRead = function(recipientId) {
    return this.updateMany(
        { recipient: recipientId, isRead: false },
        { 
            isRead: true, 
            readAt: Date.now(),
            updatedAt: Date.now()
        }
    );
};

notificationSchema.statics.deleteOldNotifications = function(daysOld = 30) {
    const cutoffDate = new Date(Date.now() - daysOld * 24 * 60 * 60 * 1000);
    return this.deleteMany({
        createdAt: { $lt: cutoffDate },
        isRead: true
    });
};

notificationSchema.statics.getNotificationStats = async function(recipientId) {
    const stats = await this.aggregate([
        { $match: { recipient: mongoose.Types.ObjectId(recipientId) } },
        {
            $group: {
                _id: null,
                total: { $sum: 1 },
                unread: { $sum: { $cond: [{ $eq: ['$isRead', false] }, 1, 0] } },
                byType: {
                    $push: {
                        type: '$type',
                        isRead: '$isRead'
                    }
                }
            }
        }
    ]);
    
    return stats[0] || { total: 0, unread: 0, byType: [] };
};

// Notification type helpers
notificationSchema.statics.createLikeNotification = function(postId, likerId, postAuthorId) {
    return this.createNotification({
        recipient: postAuthorId,
        sender: likerId,
        type: 'like_post',
        title: 'New Like',
        message: 'liked your post',
        relatedPost: postId,
        priority: 'normal'
    });
};

notificationSchema.statics.createCommentNotification = function(postId, commenterId, postAuthorId) {
    return this.createNotification({
        recipient: postAuthorId,
        sender: commenterId,
        type: 'comment_post',
        title: 'New Comment',
        message: 'commented on your post',
        relatedPost: postId,
        priority: 'normal'
    });
};

notificationSchema.statics.createFollowNotification = function(followerId, followedId) {
    return this.createNotification({
        recipient: followedId,
        sender: followerId,
        type: 'follow',
        title: 'New Follower',
        message: 'started following you',
        priority: 'normal'
    });
};

module.exports = mongoose.model('Notification', notificationSchema);
