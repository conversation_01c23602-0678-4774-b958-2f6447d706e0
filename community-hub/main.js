/**
 * Barber Brothers Community Hub - Main Entry Point
 * Initializes and manages the community hub features
 */

import AuthManager from './components/auth/AuthManager.js';
import SocialFeed from './components/feed/SocialFeed.js';
import UserProfile from './components/profile/UserProfile.js';

class CommunityHub {
    constructor() {
        this.authManager = null;
        this.socialFeed = null;
        this.currentView = 'feed';
        this.isInitialized = false;
        
        this.init();
    }

    /**
     * Initialize the community hub
     */
    async init() {
        try {
            console.log('🏛️ CommunityHub: Initializing...');
            
            // Wait for DOM to be ready
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', () => this.init());
                return;
            }
            
            // Load CSS styles
            await this.loadStyles();
            
            // Initialize authentication manager
            this.authManager = AuthManager;
            
            // Setup navigation
            this.setupNavigation();
            
            // Initialize default view
            await this.initializeDefaultView();
            
            // Setup global event listeners
            this.setupGlobalEventListeners();
            
            this.isInitialized = true;
            console.log('✅ CommunityHub: Initialized successfully');
            
            // Dispatch ready event
            window.dispatchEvent(new CustomEvent('communityHubReady'));
            
        } catch (error) {
            console.error('❌ CommunityHub: Initialization failed:', error);
        }
    }

    /**
     * Load CSS styles
     */
    async loadStyles() {
        return new Promise((resolve, reject) => {
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = '/community-hub/styles/community-hub.css';
            link.onload = resolve;
            link.onerror = reject;
            document.head.appendChild(link);
        });
    }

    /**
     * Setup navigation for community hub
     */
    setupNavigation() {
        // Add community navigation to existing navbar if not present
        const navbar = document.querySelector('.navbar-nav');
        if (navbar && !document.querySelector('.community-nav-item')) {
            const communityNavItem = document.createElement('li');
            communityNavItem.className = 'nav-item community-nav-item';
            communityNavItem.innerHTML = `
                <a class="nav-link" href="#community" data-view="feed">
                    <i class="fas fa-users me-1"></i>Community
                </a>
            `;
            
            // Insert before contact nav item
            const contactItem = navbar.querySelector('a[href="#contact"]')?.parentElement;
            if (contactItem) {
                navbar.insertBefore(communityNavItem, contactItem);
            } else {
                navbar.appendChild(communityNavItem);
            }
        }
        
        // Create community hub container if not present
        if (!document.getElementById('community-hub-container')) {
            const container = document.createElement('section');
            container.id = 'community-hub-container';
            container.className = 'community-hub';
            container.style.display = 'none';
            
            container.innerHTML = `
                <div class="community-container">
                    <div class="community-header">
                        <h1 class="community-title">
                            <i class="fas fa-users me-2"></i>
                            Barber Brothers Community
                        </h1>
                        <p class="community-subtitle">
                            Connect with fellow barbers and clients, share your work, and stay updated with the latest trends.
                        </p>
                    </div>
                    
                    <div class="community-navigation">
                        <button class="nav-btn active" data-view="feed">
                            <i class="fas fa-home me-2"></i>Feed
                        </button>
                        <button class="nav-btn" data-view="profile">
                            <i class="fas fa-user me-2"></i>Profile
                        </button>
                        <button class="nav-btn" data-view="discover">
                            <i class="fas fa-compass me-2"></i>Discover
                        </button>
                    </div>
                    
                    <div class="community-content">
                        <div id="community-feed" class="community-view active"></div>
                        <div id="community-profile" class="community-view"></div>
                        <div id="community-discover" class="community-view"></div>
                    </div>
                </div>
            `;
            
            document.body.appendChild(container);
        }
    }

    /**
     * Initialize the default view (feed)
     */
    async initializeDefaultView() {
        await this.showView('feed');
    }

    /**
     * Show a specific view
     */
    async showView(viewName) {
        try {
            console.log(`🔄 CommunityHub: Switching to ${viewName} view`);
            
            // Hide all views
            document.querySelectorAll('.community-view').forEach(view => {
                view.classList.remove('active');
                view.style.display = 'none';
            });
            
            // Update navigation
            document.querySelectorAll('.nav-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            
            const activeBtn = document.querySelector(`[data-view="${viewName}"]`);
            if (activeBtn) {
                activeBtn.classList.add('active');
            }
            
            // Show selected view
            const viewContainer = document.getElementById(`community-${viewName}`);
            if (viewContainer) {
                viewContainer.classList.add('active');
                viewContainer.style.display = 'block';
            }
            
            // Initialize view content
            switch (viewName) {
                case 'feed':
                    await this.initializeFeedView();
                    break;
                case 'profile':
                    await this.initializeProfileView();
                    break;
                case 'discover':
                    await this.initializeDiscoverView();
                    break;
            }
            
            this.currentView = viewName;
            
        } catch (error) {
            console.error(`❌ CommunityHub: Failed to show ${viewName} view:`, error);
        }
    }

    /**
     * Initialize feed view
     */
    async initializeFeedView() {
        if (!this.socialFeed) {
            this.socialFeed = new SocialFeed('community-feed');
        }
    }

    /**
     * Initialize profile view
     */
    async initializeProfileView() {
        const profileContainer = document.getElementById('community-profile');
        if (profileContainer && !profileContainer.hasChildNodes()) {
            const userProfile = new UserProfile();
            // The UserProfile component will render itself
        }
    }

    /**
     * Initialize discover view
     */
    async initializeDiscoverView() {
        const discoverContainer = document.getElementById('community-discover');
        if (discoverContainer && !discoverContainer.hasChildNodes()) {
            discoverContainer.innerHTML = `
                <div class="discover-content">
                    <h2>Discover</h2>
                    <p>Find new barbers, trending posts, and popular content in the community.</p>
                    <div class="coming-soon">
                        <i class="fas fa-tools"></i>
                        <h3>Coming Soon</h3>
                        <p>We're working on exciting discovery features!</p>
                    </div>
                </div>
            `;
        }
    }

    /**
     * Setup global event listeners
     */
    setupGlobalEventListeners() {
        // Navigation clicks
        document.addEventListener('click', (e) => {
            // Community navigation
            if (e.target.matches('[data-view]')) {
                e.preventDefault();
                const view = e.target.dataset.view;
                this.showView(view);
            }
            
            // Community hub toggle
            if (e.target.matches('a[href="#community"]')) {
                e.preventDefault();
                this.toggleCommunityHub();
            }
        });
        
        // Hash change for direct navigation
        window.addEventListener('hashchange', () => {
            if (window.location.hash === '#community') {
                this.showCommunityHub();
            } else {
                this.hideCommunityHub();
            }
        });
        
        // Check initial hash
        if (window.location.hash === '#community') {
            this.showCommunityHub();
        }
    }

    /**
     * Toggle community hub visibility
     */
    toggleCommunityHub() {
        const container = document.getElementById('community-hub-container');
        if (container) {
            if (container.style.display === 'none') {
                this.showCommunityHub();
            } else {
                this.hideCommunityHub();
            }
        }
    }

    /**
     * Show community hub
     */
    showCommunityHub() {
        const container = document.getElementById('community-hub-container');
        if (container) {
            // Hide other sections
            document.querySelectorAll('section:not(#community-hub-container)').forEach(section => {
                section.style.display = 'none';
            });
            
            // Show community hub
            container.style.display = 'block';
            
            // Update URL hash
            if (window.location.hash !== '#community') {
                window.history.pushState(null, null, '#community');
            }
            
            // Update navbar active state
            document.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('active');
            });
            
            const communityLink = document.querySelector('a[href="#community"]');
            if (communityLink) {
                communityLink.classList.add('active');
            }
        }
    }

    /**
     * Hide community hub
     */
    hideCommunityHub() {
        const container = document.getElementById('community-hub-container');
        if (container) {
            container.style.display = 'none';
            
            // Show other sections
            document.querySelectorAll('section:not(#community-hub-container)').forEach(section => {
                section.style.display = 'block';
            });
            
            // Update URL hash
            if (window.location.hash === '#community') {
                window.history.pushState(null, null, '/');
            }
        }
    }

    /**
     * Get current user
     */
    getCurrentUser() {
        return this.authManager ? this.authManager.getCurrentUser() : null;
    }

    /**
     * Check if user is authenticated
     */
    isAuthenticated() {
        return this.authManager ? this.authManager.isAuthenticated() : false;
    }

    /**
     * Cleanup
     */
    destroy() {
        if (this.socialFeed) {
            this.socialFeed.destroy();
        }
        
        if (this.authManager) {
            this.authManager.destroy();
        }
    }
}

// Initialize community hub when script loads
const communityHub = new CommunityHub();

// Export for global access
window.CommunityHub = communityHub;

export default CommunityHub;
