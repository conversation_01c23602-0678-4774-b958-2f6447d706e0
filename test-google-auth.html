<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Google Authentication - Barber Brothers Legacy</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            color: white;
            min-height: 100vh;
            padding: 2rem 0;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
        }
        .test-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 2rem;
            margin-bottom: 2rem;
        }
        .btn-test {
            margin: 0.5rem;
            min-width: 200px;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background-color: #28a745; }
        .status-error { background-color: #dc3545; }
        .status-warning { background-color: #ffc107; }
        .log-output {
            background: #000;
            color: #00ff00;
            padding: 1rem;
            border-radius: 5px;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 1rem;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="text-center mb-4">Google Authentication Test</h1>
        
        <!-- Configuration Status -->
        <div class="test-section">
            <h3>Configuration Status</h3>
            <div id="config-status">
                <div><span class="status-indicator status-warning"></span>Checking configuration...</div>
            </div>
        </div>
        
        <!-- Test Buttons -->
        <div class="test-section">
            <h3>Test Google Sign-In</h3>
            <p>Click these buttons to test the Google sign-in functionality from different sources:</p>
            
            <div class="row">
                <div class="col-md-6">
                    <button class="btn btn-danger btn-test w-100" onclick="testGoogleSignIn('navbar')">
                        <i class="fab fa-google me-2"></i>Test Navbar Sign-In
                    </button>
                </div>
                <div class="col-md-6">
                    <button class="btn btn-outline-danger btn-test w-100" onclick="testGoogleSignIn('adult')">
                        <i class="fas fa-cut me-2"></i>Test Adult Service
                    </button>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <button class="btn btn-outline-danger btn-test w-100" onclick="testGoogleSignIn('kids')">
                        <i class="fas fa-child me-2"></i>Test Kids Service
                    </button>
                </div>
                <div class="col-md-6">
                    <button class="btn btn-outline-danger btn-test w-100" onclick="testGoogleSignIn('full')">
                        <i class="fas fa-spa me-2"></i>Test Full Service
                    </button>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <button class="btn btn-outline-danger btn-test w-100" onclick="testGoogleSignIn('social')">
                        <i class="fas fa-users me-2"></i>Test Social Media
                    </button>
                </div>
                <div class="col-md-6">
                    <button class="btn btn-secondary btn-test w-100" onclick="clearAuthData()">
                        <i class="fas fa-trash me-2"></i>Clear Auth Data
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Authentication Status -->
        <div class="test-section">
            <h3>Authentication Status</h3>
            <div id="auth-status">
                <div><span class="status-indicator status-warning"></span>Checking authentication status...</div>
            </div>
            <button class="btn btn-info mt-2" onclick="checkAuthStatus()">Refresh Status</button>
        </div>
        
        <!-- Console Log -->
        <div class="test-section">
            <h3>Console Log</h3>
            <div id="log-output" class="log-output">
                <div>Test page loaded. Ready for testing...</div>
            </div>
            <button class="btn btn-secondary mt-2" onclick="clearLog()">Clear Log</button>
        </div>
        
        <!-- Back to Main Site -->
        <div class="text-center">
            <a href="index.html" class="btn btn-primary">
                <i class="fas fa-arrow-left me-2"></i>Back to Main Site
            </a>
        </div>
    </div>

    <!-- Scripts -->
    <script src="google-auth-config.js"></script>
    <script>
        // Log function
        function log(message, type = 'info') {
            const logOutput = document.getElementById('log-output');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            if (type === 'error') logEntry.style.color = '#ff6b6b';
            if (type === 'success') logEntry.style.color = '#51cf66';
            if (type === 'warning') logEntry.style.color = '#ffd43b';
            
            logOutput.appendChild(logEntry);
            logOutput.scrollTop = logOutput.scrollHeight;
        }

        // Clear log
        function clearLog() {
            document.getElementById('log-output').innerHTML = '<div>Log cleared...</div>';
        }

        // Test Google Sign-In
        function testGoogleSignIn(source) {
            log(`Testing Google Sign-In from source: ${source}`, 'info');
            
            try {
                if (typeof getGoogleAuthUrl === 'function') {
                    const authUrl = getGoogleAuthUrl(source);
                    log(`Generated auth URL: ${authUrl}`, 'success');
                    log(`Redirecting to Google...`, 'info');
                    
                    // Store source for testing
                    localStorage.setItem('signInSource', source);
                    
                    // Redirect to Google
                    window.location.href = authUrl;
                } else {
                    log('ERROR: getGoogleAuthUrl function not available', 'error');
                    log('Make sure google-auth-config.js is loaded properly', 'warning');
                }
            } catch (error) {
                log(`ERROR: ${error.message}`, 'error');
            }
        }

        // Clear authentication data
        function clearAuthData() {
            localStorage.removeItem('googleAuthData');
            localStorage.removeItem('authSuccess');
            localStorage.removeItem('signInSource');
            log('Authentication data cleared', 'success');
            checkAuthStatus();
        }

        // Check authentication status
        function checkAuthStatus() {
            const authData = localStorage.getItem('googleAuthData');
            const authSuccess = localStorage.getItem('authSuccess');
            const signInSource = localStorage.getItem('signInSource');
            
            const statusDiv = document.getElementById('auth-status');
            
            if (authSuccess === 'true' && authData) {
                try {
                    const data = JSON.parse(authData);
                    statusDiv.innerHTML = `
                        <div><span class="status-indicator status-success"></span>Authenticated successfully</div>
                        <div class="mt-2">
                            <strong>Source:</strong> ${data.source || 'unknown'}<br>
                            <strong>Timestamp:</strong> ${new Date(data.timestamp).toLocaleString()}<br>
                            <strong>Has Auth Code:</strong> ${data.code ? 'Yes' : 'No'}
                        </div>
                    `;
                    log('User is authenticated', 'success');
                } catch (error) {
                    statusDiv.innerHTML = `<div><span class="status-indicator status-error"></span>Error parsing auth data</div>`;
                    log(`Error parsing auth data: ${error.message}`, 'error');
                }
            } else {
                statusDiv.innerHTML = `<div><span class="status-indicator status-warning"></span>Not authenticated</div>`;
                log('User is not authenticated', 'warning');
            }
        }

        // Check configuration
        function checkConfiguration() {
            const configDiv = document.getElementById('config-status');
            
            if (typeof GOOGLE_AUTH_CONFIG !== 'undefined') {
                const isValidClientId = GOOGLE_AUTH_CONFIG.clientId && 
                    GOOGLE_AUTH_CONFIG.clientId !== '946338896038-your-actual-client-id.apps.googleusercontent.com' &&
                    GOOGLE_AUTH_CONFIG.clientId.includes('.apps.googleusercontent.com');
                
                if (isValidClientId) {
                    configDiv.innerHTML = `
                        <div><span class="status-indicator status-success"></span>Configuration loaded successfully</div>
                        <div class="mt-2">
                            <strong>Client ID:</strong> ${GOOGLE_AUTH_CONFIG.clientId}<br>
                            <strong>Redirect URI:</strong> ${GOOGLE_AUTH_CONFIG.redirectUri}<br>
                            <strong>Scopes:</strong> ${GOOGLE_AUTH_CONFIG.scopes.join(', ')}
                        </div>
                    `;
                    log('Google Auth configuration is valid', 'success');
                } else {
                    configDiv.innerHTML = `
                        <div><span class="status-indicator status-warning"></span>Configuration loaded but Client ID needs to be updated</div>
                        <div class="mt-2">
                            <strong>Current Client ID:</strong> ${GOOGLE_AUTH_CONFIG.clientId}<br>
                            <em>Please update this with your actual Google OAuth Client ID</em>
                        </div>
                    `;
                    log('Configuration loaded but Client ID is placeholder', 'warning');
                }
            } else {
                configDiv.innerHTML = `<div><span class="status-indicator status-error"></span>Configuration not loaded</div>`;
                log('ERROR: Google Auth configuration not loaded', 'error');
            }
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            log('Test page initialized', 'info');
            checkConfiguration();
            checkAuthStatus();
            
            // Check if we just returned from authentication
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.get('auth') === 'success') {
                log('Returned from successful authentication', 'success');
                checkAuthStatus();
            }
        });
    </script>
</body>
</html>
