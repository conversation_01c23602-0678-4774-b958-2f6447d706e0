<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Community Hub Test</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Community Hub CSS -->
    <link href="community-hub/styles/community-hub.css" rel="stylesheet">
    
    <style>
        body {
            background-color: #1a1a1a;
            color: #ffffff;
            font-family: 'Roboto', sans-serif;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .test-section {
            background-color: #2a2a2a;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid #444;
        }
        
        .test-status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .test-success {
            background-color: #28a745;
            color: white;
        }
        
        .test-error {
            background-color: #dc3545;
            color: white;
        }
        
        .test-warning {
            background-color: #ffc107;
            color: black;
        }
        
        .test-info {
            background-color: #17a2b8;
            color: white;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="text-center mb-4">
            <i class="fas fa-users me-2"></i>
            Community Hub Integration Test
        </h1>
        
        <div class="test-section">
            <h2><i class="fas fa-cog me-2"></i>System Status</h2>
            <div id="system-status">
                <div class="test-info">
                    <i class="fas fa-spinner fa-spin me-2"></i>
                    Initializing tests...
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2><i class="fas fa-fire me-2"></i>Firebase Connection</h2>
            <div id="firebase-status">
                <div class="test-info">
                    <i class="fas fa-spinner fa-spin me-2"></i>
                    Testing Firebase connection...
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2><i class="fas fa-shield-alt me-2"></i>Authentication Service</h2>
            <div id="auth-status">
                <div class="test-info">
                    <i class="fas fa-spinner fa-spin me-2"></i>
                    Testing authentication service...
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2><i class="fas fa-comments me-2"></i>Post Service</h2>
            <div id="post-status">
                <div class="test-info">
                    <i class="fas fa-spinner fa-spin me-2"></i>
                    Testing post service...
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2><i class="fas fa-puzzle-piece me-2"></i>Component Loading</h2>
            <div id="component-status">
                <div class="test-info">
                    <i class="fas fa-spinner fa-spin me-2"></i>
                    Testing component loading...
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2><i class="fas fa-play me-2"></i>Community Hub Demo</h2>
            <div id="demo-container">
                <button id="start-demo" class="btn btn-danger btn-lg">
                    <i class="fas fa-rocket me-2"></i>
                    Start Community Hub Demo
                </button>
            </div>
        </div>
        
        <!-- Community Hub Container -->
        <div id="community-hub-test-container" style="display: none;">
            <!-- Community hub will be rendered here -->
        </div>
    </div>
    
    <!-- Firebase Scripts -->
    <script src="https://www.gstatic.com/firebasejs/9.6.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.6.1/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.6.1/firebase-firestore-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.6.1/firebase-storage-compat.js"></script>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Test Script -->
    <script type="module">
        // Test configuration
        const testConfig = {
            firebase: {
                apiKey: "AIzaSyAHqpO8PPXmZIfype7dsViz3chKcmLdmpY",
                authDomain: "barber-brothers-legacy.firebaseapp.com",
                projectId: "barber-brothers-legacy",
                storageBucket: "barber-brothers-legacy.appspot.com",
                messagingSenderId: "946338896038",
                appId: "1:946338896038:web:d65f5bef7973127d1abc67"
            }
        };
        
        // Test results
        const testResults = {
            system: false,
            firebase: false,
            auth: false,
            post: false,
            components: false
        };
        
        // Update test status
        function updateStatus(testName, status, message) {
            const container = document.getElementById(`${testName}-status`);
            const statusClass = status === 'success' ? 'test-success' : 
                               status === 'error' ? 'test-error' : 
                               status === 'warning' ? 'test-warning' : 'test-info';
            
            const icon = status === 'success' ? 'fa-check' : 
                        status === 'error' ? 'fa-times' : 
                        status === 'warning' ? 'fa-exclamation-triangle' : 'fa-info';
            
            container.innerHTML = `
                <div class="${statusClass}">
                    <i class="fas ${icon} me-2"></i>
                    ${message}
                </div>
            `;
            
            if (status === 'success') {
                testResults[testName] = true;
            }
        }
        
        // Test system requirements
        function testSystem() {
            updateStatus('system', 'info', 'Checking system requirements...');
            
            const checks = [];
            
            // Check if ES6 modules are supported
            if (typeof Symbol !== 'undefined') {
                checks.push('✓ ES6 Support');
            } else {
                checks.push('✗ ES6 Support');
            }
            
            // Check if fetch is available
            if (typeof fetch !== 'undefined') {
                checks.push('✓ Fetch API');
            } else {
                checks.push('✗ Fetch API');
            }
            
            // Check if localStorage is available
            if (typeof localStorage !== 'undefined') {
                checks.push('✓ Local Storage');
            } else {
                checks.push('✗ Local Storage');
            }
            
            const allPassed = checks.every(check => check.startsWith('✓'));
            
            updateStatus('system', allPassed ? 'success' : 'error', 
                `System checks: ${checks.join(', ')}`);
        }
        
        // Test Firebase connection
        function testFirebase() {
            updateStatus('firebase', 'info', 'Initializing Firebase...');
            
            try {
                // Initialize Firebase
                if (!firebase.apps.length) {
                    firebase.initializeApp(testConfig.firebase);
                }
                
                const auth = firebase.auth();
                const firestore = firebase.firestore();
                const storage = firebase.storage();
                
                updateStatus('firebase', 'success', 
                    'Firebase initialized successfully (Auth, Firestore, Storage)');
                
            } catch (error) {
                updateStatus('firebase', 'error', 
                    `Firebase initialization failed: ${error.message}`);
            }
        }
        
        // Test authentication service
        async function testAuthService() {
            updateStatus('auth', 'info', 'Loading authentication service...');
            
            try {
                // Import AuthService
                const { default: AuthService } = await import('./community-hub/services/AuthService.js');
                
                // Wait for initialization
                await new Promise(resolve => {
                    const checkInit = setInterval(() => {
                        if (AuthService.isInitialized) {
                            clearInterval(checkInit);
                            resolve();
                        }
                    }, 100);
                });
                
                updateStatus('auth', 'success', 
                    'Authentication service loaded and initialized');
                
            } catch (error) {
                updateStatus('auth', 'error', 
                    `Authentication service failed: ${error.message}`);
            }
        }
        
        // Test post service
        async function testPostService() {
            updateStatus('post', 'info', 'Loading post service...');
            
            try {
                // Import PostService
                const { default: PostService } = await import('./community-hub/services/PostService.js');
                
                // Wait for initialization
                await new Promise(resolve => {
                    const checkInit = setInterval(() => {
                        if (PostService.isInitialized) {
                            clearInterval(checkInit);
                            resolve();
                        }
                    }, 100);
                });
                
                updateStatus('post', 'success', 
                    'Post service loaded and initialized');
                
            } catch (error) {
                updateStatus('post', 'error', 
                    `Post service failed: ${error.message}`);
            }
        }
        
        // Test component loading
        async function testComponents() {
            updateStatus('component', 'info', 'Loading community hub components...');
            
            try {
                // Import main community hub
                const { default: CommunityHub } = await import('./community-hub/main.js');
                
                // Wait for initialization
                await new Promise(resolve => {
                    const checkInit = setInterval(() => {
                        if (CommunityHub.isInitialized) {
                            clearInterval(checkInit);
                            resolve();
                        }
                    }, 100);
                });
                
                updateStatus('component', 'success', 
                    'Community hub components loaded successfully');
                
                // Enable demo button
                document.getElementById('start-demo').disabled = false;
                
            } catch (error) {
                updateStatus('component', 'error', 
                    `Component loading failed: ${error.message}`);
            }
        }
        
        // Start demo
        function startDemo() {
            const demoContainer = document.getElementById('community-hub-test-container');
            demoContainer.style.display = 'block';
            demoContainer.scrollIntoView({ behavior: 'smooth' });
            
            // Update demo button
            const demoButton = document.getElementById('start-demo');
            demoButton.innerHTML = '<i class="fas fa-check me-2"></i>Demo Running';
            demoButton.classList.remove('btn-danger');
            demoButton.classList.add('btn-success');
            demoButton.disabled = true;
        }
        
        // Run all tests
        async function runTests() {
            testSystem();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            testFirebase();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testAuthService();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testPostService();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testComponents();
        }
        
        // Event listeners
        document.getElementById('start-demo').addEventListener('click', startDemo);
        
        // Start tests when page loads
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(runTests, 1000);
        });
    </script>
</body>
</html>
