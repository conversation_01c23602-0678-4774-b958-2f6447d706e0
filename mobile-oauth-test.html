<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mobile OAuth Test - Barber Brothers Legacy</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            color: white;
            min-height: 100vh;
            padding: 20px;
            font-size: 16px;
        }
        .test-container {
            max-width: 100%;
            margin: 0 auto;
        }
        .test-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .btn-large {
            padding: 15px 30px;
            font-size: 18px;
            margin: 10px 0;
            width: 100%;
        }
        .info-box {
            background: rgba(0, 123, 255, 0.1);
            border: 1px solid #007bff;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        .error-box {
            background: rgba(220, 53, 69, 0.1);
            border: 1px solid #dc3545;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        .success-box {
            background: rgba(40, 167, 69, 0.1);
            border: 1px solid #28a745;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        .code-display {
            background: #2d2d2d;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            word-break: break-all;
            white-space: pre-wrap;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background-color: #28a745; }
        .status-error { background-color: #dc3545; }
        .status-warning { background-color: #ffc107; }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="text-center mb-4">
            <h1>📱 Mobile OAuth Test</h1>
            <p>Barber Brothers Legacy - Mobile Sign-In Diagnostics</p>
        </div>

        <div class="test-card">
            <h3>🔍 Device Information</h3>
            <div id="device-info" class="info-box">
                <div>Loading device information...</div>
            </div>
        </div>

        <div class="test-card">
            <h3>🌐 Network & Environment</h3>
            <div id="network-info" class="info-box">
                <div>Checking network status...</div>
            </div>
        </div>

        <div class="test-card">
            <h3>🔧 OAuth Configuration</h3>
            <div id="config-status" class="info-box">
                <div>Validating OAuth configuration...</div>
            </div>
            <button class="btn btn-primary btn-large" onclick="testConfiguration()">
                🔍 Test OAuth Config
            </button>
        </div>

        <div class="test-card">
            <h3>🚀 OAuth Flow Test</h3>
            <div id="oauth-status" class="info-box">
                <div>Ready to test OAuth flow</div>
            </div>
            <button class="btn btn-success btn-large" onclick="testOAuthFlow()">
                🔐 Test Google Sign-In
            </button>
            <button class="btn btn-warning btn-large" onclick="generateOAuthURL()">
                🔗 Generate OAuth URL
            </button>
        </div>

        <div class="test-card">
            <h3>📋 Test Results</h3>
            <div id="test-results">
                <div class="info-box">No tests run yet. Click the buttons above to start testing.</div>
            </div>
        </div>

        <div class="test-card">
            <h3>🛠️ Troubleshooting</h3>
            <button class="btn btn-secondary btn-large" onclick="clearAllData()">
                🗑️ Clear All Auth Data
            </button>
            <button class="btn btn-info btn-large" onclick="copyDiagnostics()">
                📋 Copy Diagnostics
            </button>
        </div>

        <div class="text-center mt-4">
            <a href="/" class="btn btn-outline-light">← Back to Website</a>
        </div>
    </div>

    <script src="google-auth-config.js"></script>
    <script>
        let diagnosticData = {};

        document.addEventListener('DOMContentLoaded', function() {
            loadDeviceInfo();
            loadNetworkInfo();
            loadConfigStatus();
        });

        function addResult(type, message) {
            const resultsDiv = document.getElementById('test-results');
            const statusClass = type === 'success' ? 'success-box' : type === 'error' ? 'error-box' : 'info-box';
            const statusIcon = type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️';
            
            const resultDiv = document.createElement('div');
            resultDiv.className = statusClass;
            resultDiv.innerHTML = `${statusIcon} ${message}`;
            
            resultsDiv.appendChild(resultDiv);
            
            // Scroll to bottom
            resultDiv.scrollIntoView({ behavior: 'smooth' });
        }

        function loadDeviceInfo() {
            const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
            const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
            const isAndroid = /Android/.test(navigator.userAgent);
            
            diagnosticData.device = {
                userAgent: navigator.userAgent,
                isMobile: isMobile,
                isIOS: isIOS,
                isAndroid: isAndroid,
                platform: navigator.platform,
                language: navigator.language,
                cookieEnabled: navigator.cookieEnabled,
                onLine: navigator.onLine,
                viewport: {
                    width: window.innerWidth,
                    height: window.innerHeight,
                    devicePixelRatio: window.devicePixelRatio
                },
                touchSupport: 'ontouchstart' in window
            };

            const deviceInfo = document.getElementById('device-info');
            deviceInfo.innerHTML = `
                <div><span class="status-indicator ${isMobile ? 'status-success' : 'status-warning'}"></span><strong>Device Type:</strong> ${isMobile ? 'Mobile' : 'Desktop'}</div>
                <div><span class="status-indicator ${isIOS ? 'status-success' : 'status-warning'}"></span><strong>iOS:</strong> ${isIOS}</div>
                <div><span class="status-indicator ${isAndroid ? 'status-success' : 'status-warning'}"></span><strong>Android:</strong> ${isAndroid}</div>
                <div><span class="status-indicator status-success"></span><strong>Platform:</strong> ${navigator.platform}</div>
                <div><span class="status-indicator ${navigator.cookieEnabled ? 'status-success' : 'status-error'}"></span><strong>Cookies:</strong> ${navigator.cookieEnabled ? 'Enabled' : 'Disabled'}</div>
                <div><span class="status-indicator ${navigator.onLine ? 'status-success' : 'status-error'}"></span><strong>Online:</strong> ${navigator.onLine}</div>
                <div><span class="status-indicator status-success"></span><strong>Viewport:</strong> ${window.innerWidth}x${window.innerHeight}</div>
                <div><span class="status-indicator ${diagnosticData.device.touchSupport ? 'status-success' : 'status-warning'}"></span><strong>Touch:</strong> ${diagnosticData.device.touchSupport ? 'Supported' : 'Not detected'}</div>
            `;
        }

        function loadNetworkInfo() {
            const isHTTPS = window.location.protocol === 'https:';
            const isLocalhost = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
            const currentDomain = window.location.hostname;
            const expectedDomain = 'www.barberbrotherz.com';
            const domainMatch = currentDomain === expectedDomain;

            diagnosticData.network = {
                protocol: window.location.protocol,
                hostname: window.location.hostname,
                origin: window.location.origin,
                href: window.location.href,
                isHTTPS: isHTTPS,
                isLocalhost: isLocalhost,
                domainMatch: domainMatch
            };

            const networkInfo = document.getElementById('network-info');
            networkInfo.innerHTML = `
                <div><span class="status-indicator ${isHTTPS || isLocalhost ? 'status-success' : 'status-error'}"></span><strong>Protocol:</strong> ${window.location.protocol} ${isHTTPS || isLocalhost ? '(OAuth Compatible)' : '(Requires HTTPS)'}</div>
                <div><span class="status-indicator ${domainMatch ? 'status-success' : 'status-warning'}"></span><strong>Domain:</strong> ${currentDomain} ${domainMatch ? '(Correct)' : '(Expected: ' + expectedDomain + ')'}</div>
                <div><span class="status-indicator status-success"></span><strong>Origin:</strong> ${window.location.origin}</div>
                <div><span class="status-indicator status-success"></span><strong>Full URL:</strong> ${window.location.href}</div>
            `;
        }

        function loadConfigStatus() {
            try {
                const configLoaded = typeof GOOGLE_AUTH_CONFIG !== 'undefined';
                const functionLoaded = typeof getGoogleAuthUrl === 'function';
                const clientId = configLoaded ? GOOGLE_AUTH_CONFIG.clientId : 'Not loaded';
                const redirectUri = configLoaded ? GOOGLE_AUTH_CONFIG.redirectUri : 'Not loaded';

                diagnosticData.config = {
                    configLoaded: configLoaded,
                    functionLoaded: functionLoaded,
                    clientId: clientId,
                    redirectUri: redirectUri
                };

                const configStatus = document.getElementById('config-status');
                configStatus.innerHTML = `
                    <div><span class="status-indicator ${configLoaded ? 'status-success' : 'status-error'}"></span><strong>Config Loaded:</strong> ${configLoaded}</div>
                    <div><span class="status-indicator ${functionLoaded ? 'status-success' : 'status-error'}"></span><strong>Function Available:</strong> ${functionLoaded}</div>
                    <div><span class="status-indicator ${clientId.includes('424859813197-45iu9t509loarues2v8vnmek3b3qi55b') ? 'status-success' : 'status-error'}"></span><strong>Client ID:</strong> ${clientId.substring(0, 20)}...</div>
                    <div><span class="status-indicator ${redirectUri.includes('barberbrotherz.com') ? 'status-success' : 'status-warning'}"></span><strong>Redirect URI:</strong> ${redirectUri}</div>
                `;
            } catch (error) {
                const configStatus = document.getElementById('config-status');
                configStatus.innerHTML = `<div class="error-box">❌ Error loading configuration: ${error.message}</div>`;
            }
        }

        function testConfiguration() {
            addResult('info', 'Testing OAuth configuration...');
            
            try {
                if (typeof GOOGLE_AUTH_CONFIG === 'undefined') {
                    throw new Error('GOOGLE_AUTH_CONFIG not loaded');
                }
                
                if (typeof getGoogleAuthUrl !== 'function') {
                    throw new Error('getGoogleAuthUrl function not available');
                }
                
                const testUrl = getGoogleAuthUrl('mobile-test');
                addResult('success', 'OAuth configuration test passed');
                addResult('info', `Generated test URL: ${testUrl.substring(0, 100)}...`);
                
            } catch (error) {
                addResult('error', `Configuration test failed: ${error.message}`);
            }
        }

        function generateOAuthURL() {
            try {
                const oauthUrl = getGoogleAuthUrl('mobile-test');
                addResult('success', 'OAuth URL generated successfully');
                
                const urlDiv = document.createElement('div');
                urlDiv.className = 'code-display';
                urlDiv.textContent = oauthUrl;
                
                const resultsDiv = document.getElementById('test-results');
                resultsDiv.appendChild(urlDiv);
                
                // Copy to clipboard if possible
                if (navigator.clipboard) {
                    navigator.clipboard.writeText(oauthUrl).then(() => {
                        addResult('success', 'OAuth URL copied to clipboard');
                    });
                }
                
            } catch (error) {
                addResult('error', `Failed to generate OAuth URL: ${error.message}`);
            }
        }

        function testOAuthFlow() {
            if (confirm('This will redirect you to Google Sign-In. Continue?')) {
                try {
                    addResult('info', 'Starting OAuth flow test...');
                    const authUrl = getGoogleAuthUrl('mobile-oauth-test');
                    
                    // Store test data
                    localStorage.setItem('mobileOAuthTest', JSON.stringify({
                        timestamp: Date.now(),
                        userAgent: navigator.userAgent,
                        origin: window.location.origin
                    }));
                    
                    addResult('success', 'Redirecting to Google...');
                    setTimeout(() => {
                        window.location.href = authUrl;
                    }, 1000);
                    
                } catch (error) {
                    addResult('error', `OAuth flow test failed: ${error.message}`);
                }
            }
        }

        function clearAllData() {
            localStorage.removeItem('googleAuthData');
            localStorage.removeItem('authSuccess');
            localStorage.removeItem('userProfile');
            localStorage.removeItem('signInSource');
            localStorage.removeItem('mobileOAuthTest');
            
            addResult('success', 'All authentication data cleared');
        }

        function copyDiagnostics() {
            const diagnostics = {
                timestamp: new Date().toISOString(),
                url: window.location.href,
                device: diagnosticData.device,
                network: diagnosticData.network,
                config: diagnosticData.config,
                localStorage: {
                    googleAuthData: localStorage.getItem('googleAuthData'),
                    authSuccess: localStorage.getItem('authSuccess'),
                    userProfile: localStorage.getItem('userProfile')
                }
            };
            
            const diagnosticsText = JSON.stringify(diagnostics, null, 2);
            
            if (navigator.clipboard) {
                navigator.clipboard.writeText(diagnosticsText).then(() => {
                    addResult('success', 'Diagnostic data copied to clipboard');
                });
            } else {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = diagnosticsText;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                addResult('success', 'Diagnostic data copied (fallback method)');
            }
        }
    </script>
</body>
</html>
