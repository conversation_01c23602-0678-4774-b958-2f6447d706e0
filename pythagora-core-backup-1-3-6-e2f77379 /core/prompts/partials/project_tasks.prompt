{# This is actually creation of tasks and not epics. Reason why this prompt uses word "epic" instead of "task" is that LLM gives very detailed description and creates very big plan if we ask him to create tasks. When asked to create epics he focuses on much bigger picture and gives high level description which is what we want. #}
# Rules for creating epics
~~~START_OF_RULES_FOR_CREATING_EPICS~~~

## Rule #1
Every epic must have only coding involved. There should never be a epic that is only testing or ensuring something works. There shouldn't be a epic for researching, deployment, writing documentation, testing or anything that is not writing the actual code. Testing if app works will be done as part of each epic.
Do not leave anything for interpretation, e.g. if something can be done in multiple ways, specify which way should be used and be as clear as possible.

## Rule #2
This rule applies to the number of epics you will create.
Every app should have different number of epics depending on complexity. Think epic by epic and create the minimum number of epics that are needed to develop this app.
Simple apps should have only 1 epic. More complex apps should have more epics. Do not create more epics than needed.

## Rule #3
This rule applies to writing epic 'description'.
Every epic must have a clear, high level, and short 1-2 sentence 'description'. It must be very clear so that even non technical users who are reviewing it and just moved to this project can understand what is goal for the epic.

** MOST IMPORTANT RULES **
## Rule #4 (MOST IMPORTANT RULE)
This rule applies to thinking about the API endpoints specified above between START_OF_FRONTEND_API_FILES and END_OF_FRONTEND_API_FILES.
Each epic must be related to one or more API endpoints that are called from the frontend files. Go through all API endpoints called from the frontend - if there are multiple endpoints related to a single entity (for example, CRUD operations on a database model), you can put them in the same epic but otherwise, make sure that API endpoints for different entities are in different epics. The epics you create **MUST** cover **ALL** API endpoints mentioned in the frontend files above.

## Rule #5 (MOST IMPORTANT RULE)
This rule applies to order of epics.
Epics will be executed in same order that you output them. You must order them in a logical way so that epics that depend on other functionalities are implemented in later stage.
{% if task_type  == 'app' %}
### Rule 5.1 - Updating the User model
Ask yourself: "Does the User model in this app needs any additional field other then the ones that are already implemented (email, password, createdAt, updatedAt, refreshToken, isActive, and lastLoginAt)?". If the answer is yes, the user model needs to be updated, so the first epic **MUST** be to update the user model. You **MUST** think about the user model because if this step is overlooked, the entire app won't work.
{% endif %}
### Rule 5.{% if task_type  == 'app' %}2{% else %}1{% endif %} - scripts
After updating the user model, the next epic **MUST** be to create any necessary scripts (if there are any). For example, a script to create an admin user, a script to seed the database, etc.

### Rule 5.{% if task_type  == 'app' %}3{% else %}2{% endif %} - other epics
Finally, all other epics must be about creating database models and CRUD operations (each epic must contain CRUD operations only for one single model - never for multiple). Pay attention to the API requests inside files in `client/api/` folder because they are currently using mocked data and whenever you implement an API endpoint, you just need to replace the mocked data with the real API request to the backend.

## Rule #6
Create epics for things that are not yet implemented. Do not reimplement what's already done. If something is already implemented, do not create epic for it. Continue from the implementation already there.

~~~END_OF_RULES_FOR_CREATING_EPICS~~~
