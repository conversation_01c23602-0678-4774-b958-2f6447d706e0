How can a human user test if this task was completed successfully?

Please list actions, step by step, in order, that the user should take to verify the task. After each action, describe what the expected response is.

**IMPORTANT**

Follow these important rules when compiling a list of actions the user will take:

1. Actions must be as specific as possible. You don't want the user to have to think anything through but rather that they just follow your instructions.
2. In case this task can be tested by making an API request, you should always prefer to test functionality in the browser. In case you can't do that, do not suggest how can a request be made with Postman but rather write a full cURL command that the user can just run.
3. Do not require the user to write any code or edit files to test this task.
4. If the user must run a command, assume the user already has a terminal opened in the project root directory (no need to instruct the user "open the terminal" or "make sure you're in the project directory")
5. The user is using {{ os }}, so the commands must run on that operating system
6. Assume system services, such as the database, are already set up and running. Don't ask user to install or run any software other than the app they're testing.
7. Don't ask the user to test things which aren't implemented yet (eg. opening a theoretical web page that doesn't exist yet, or clicking on a button that isn't implemented yet)
8. Think about if there is something that user needs to do manually to make the next testing step work
9. The human has the option to press the "Start App" button so don't instruct them to start the app in any way.
10. If the user needs to run a database command, make sure to specify the entire command that needs to be run

Remember, these rules are very important and you must follow them!

Here is an example output with a few user steps:
---example---
{
    "steps": [
        {
            "title": "Submit the form",
            "action": "Open your web browser and visit 'http://localhost:5173/form'. Click on the 'Submit' button in the web form",
            "result": "Form is submitted, the page is reloaded, and 'Thank you' message is shown",
        },
        {
            "title": "Check email",
            "action": "Check your email inbox for the magic link. Click on the magic link to log in.",
            "result": "You should be redirected back to the home page. The login status should now display 'Logged in as [your email]' and the 'Logout' button should be visible.",
        },
        {
            "title": "Log out",
            "action": "Click on the 'Logout' button",
            "result": "You should be redirected back to the home page. You should not be able to access the form and the 'Login' button should be visible.",
        }
    ]
}
---end_of_example---

If nothing needs to be tested for this task, instead of outputting the steps, just output an empty list like this:
---example_when_test_not_needed---
{
    "steps": []
}
---end_of_example_when_test_not_needed---

When you think about the testing instructions for the human, keep in mind the tasks that have been already implemented, the task that the human needs to test right now, and the tasks that are still not implemented. If something is not implemented yet, the user will not be able to test a functionality related to that. For example, if a task is to implement a feature that enables the user to create a company record and if you see that the feature to retrieve a list of company records is still not implemented, you cannot tell the human to open the page for viewing company records because it's still not implemented. In this example, you should tell the human to look into a database or some other way that they can verify if the company records are created. The current situation is like this:
Here are the tasks that are implemented:
```
{% for task in state.tasks %}
{% if loop.index - 1 < current_task_index %}
{{ loop.index }}. {{ task.description }}{% if task.get("status") == "done" %} (completed){% endif %}

{% endif %}{% endfor %}
```

Here is the task that the human needs to test:
```
{{ current_task_index + 1 }}. {{ task.description }}
```

And here are the tasks that are still NOT implemented:
```
{% for task in state.tasks %}
{% if loop.index - 1 > current_task_index %}
{{ loop.index }}. {{ task.description }}{% if task.get("status") == "done" %} (completed){% endif %}

{% endif %}{% endfor %}
```

Knowing these rules, tell me, please list actions, step by step, in order, that the user should take to verify the task.
