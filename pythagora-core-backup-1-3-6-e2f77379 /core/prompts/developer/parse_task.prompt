For the implementation instructions defined below, create a list of actionable steps that will be executed by a machine.

~~~IMPLEMENTATION_INSTRUCTIONS~~~
{{ implementation_instructions }}
~~~END_OF_IMPLEMENTATION_INSTRUCTIONS~~~

Each actionable step can be either:

* `command`
 - command to run
 - assume current working directory is project root folder, which means you MUST add `cd server && <cmd>` or `cd client && <cmd>` if they have to be executed inside `./server` or `./client` folders
 - must be able to run on a {{ os }} machine

* `save_file`
 - create or update ONE file (only provide file path, not contents)
 - **IMPORTANT**: If multiple changes are required for same file, you must provide single `save_file` step for each file.

* `human_intervention`
 - if you need the human to do something other than coding or testing, use this type of step and explain in details what you want the human to do
 - NEVER use `human_intervention` for testing, as testing will be done separately by a dedicated QA after all the steps are done
 - NEVER use `human_intervention` to ask the human to write or review code
 - **IMPORTANT**: Remember, NEVER output human intervention steps to do manual tests or coding tasks, even if the previous message asks for it! The testing will be done *after* these steps and you MUST NOT include testing in these steps.
 - {% include "partials/human_intervention_explanation.prompt" %}

Examples:
------------------------example_1---------------------------
```
{
  "tasks": [
    {
      "type": "save_file",
      "save_file": {
        "path": "server/server.js"
      },
    },
    {
      "type": "command",
      "command": {
        "command": "cd server && npm install puppeteer",
        "timeout": 30,
        "success_message": "",
        "command_id": "install_puppeteer"
      }
    },
    {
      "type": "command",
      "command": {
        "command": "cd client && npm install lucide-react",
        "timeout": 30,
        "success_message": "",
        "command_id": "install_lucide_react"
      }
    },
    {
     "type": "human_intervention",
     "human_intervention_description": "1. Open the AWS Management Console (https://aws.amazon.com/console/). 2. Navigate to the S3 service and create a new bucket. 3. Configure the bucket with public read access by adjusting the permissions. 4. Upload your static website files to the bucket. 5. Enable static website hosting in the bucket settings and note the website endpoint URL. 6. Add the endpoint URL to your application’s configuration file as: WEBSITE_URL=your_website_endpoint"
    }
  ]
}
```
------------------------end_of_example_1---------------------------

{% include "partials/execution_order.prompt" %}