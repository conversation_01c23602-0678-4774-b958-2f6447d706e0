const fetch = require('node-fetch');

// Load API key from environment variable or config
const BRAVE_SEARCH_API_KEY = process.env.BRAVE_SEARCH_API_KEY || require('./config').braveSearchApiKey;

/**
 * Query Brave Search API
 * @param {string} query - The search query
 * @param {number} [count=10] - Number of results to return
 * @returns {Promise<object>} - Brave Search API response
 */
async function braveSearch(query, count = 10) {
    const endpoint = `https://api.search.brave.com/res/v1/web/search?q=${encodeURIComponent(query)}&count=${count}`;
    const response = await fetch(endpoint, {
        headers: {
            'Accept': 'application/json',
            'X-Subscription-Token': BRAVE_SEARCH_API_KEY
        }
    });
    if (!response.ok) {
        throw new Error(`Brave Search API error: ${response.status} ${response.statusText}`);
    }
    return response.json();
}

module.exports = braveSearch; 