import { useState, useEffect } from 'react';
import {
  collection,
  addDoc,
  query,
  orderBy,
  onSnapshot,
  updateDoc,
  doc,
  increment,
  arrayUnion,
  arrayRemove,
  getDoc
} from 'firebase/firestore';

// Get Firebase instances from window
const { auth, db } = window.firebase;

export default function CommunityFeed() {
  const [posts, setPosts] = useState([]);
  const [newPost, setNewPost] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [comments, setComments] = useState({});
  const [newComments, setNewComments] = useState({});

  // Load posts in real-time
  useEffect(() => {
    const q = query(
      collection(db, 'posts'),
      orderBy('createdAt', 'desc')
    );

    const unsubscribe = onSnapshot(q, (querySnapshot) => {
      const postsData = [];
      querySnapshot.forEach((doc) => {
        postsData.push({ id: doc.id, ...doc.data() });
      });
      setPosts(postsData);
    });

    // Cleanup subscription on unmount
    return () => unsubscribe();
  }, []);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    if (!auth.currentUser) {
      setError('You must be logged in to post');
      setLoading(false);
      return;
    }

    try {
      await addDoc(collection(db, 'posts'), {
        content: newPost,
        authorId: auth.currentUser.uid,
        authorName: auth.currentUser.displayName,
        createdAt: new Date().toISOString(),
        likes: 0,
        commentCount: 0
      });

      setNewPost('');
    } catch (err) {
      setError('Failed to create post: ' + err.message);
    }

    setLoading(false);
  };

  const fetchComments = async (postId) => {
    const q = query(
      collection(db, 'posts', postId, 'comments'),
      orderBy('createdAt', 'asc')
    );
    
    const unsubscribe = onSnapshot(q, (querySnapshot) => {
      const commentsData = [];
      querySnapshot.forEach((doc) => {
        commentsData.push({ id: doc.id, ...doc.data() });
      });
      setComments(prev => ({...prev, [postId]: commentsData}));
    });

    return unsubscribe;
  };

  const handleCommentSubmit = async (postId) => {
    if (!newComments[postId]?.trim() || !auth.currentUser) return;

    try {
      await addDoc(collection(db, 'posts', postId, 'comments'), {
        content: newComments[postId],
        authorId: auth.currentUser.uid,
        authorName: auth.currentUser.displayName,
        createdAt: new Date().toISOString()
      });

      // Update comment count
      await updateDoc(doc(db, 'posts', postId), {
        commentCount: increment(1)
      });

      setNewComments(prev => ({...prev, [postId]: ''}));
    } catch (err) {
      setError('Failed to add comment: ' + err.message);
    }
  };

  // Load comments when post is rendered
  useEffect(() => {
    const unsubscribers = {};
    posts.forEach(post => {
      unsubscribers[post.id] = fetchComments(post.id);
    });

    return () => {
      Object.values(unsubscribers).forEach(unsub => unsub());
    };
  }, [posts]);

  const handleLike = async (postId, currentLikes) => {
    if (!auth.currentUser) {
      setError('You must be logged in to like posts');
      return;
    }

    const postRef = doc(db, 'posts', postId);
    const userId = auth.currentUser.uid;

    try {
      // Check if user already liked the post
      const postDoc = await getDoc(postRef);
      const likedBy = postDoc.data()?.likedBy || [];
      const alreadyLiked = likedBy.includes(userId);

      await updateDoc(postRef, {
        likes: alreadyLiked ? increment(-1) : increment(1),
        likedBy: alreadyLiked
          ? arrayRemove(userId)
          : arrayUnion(userId)
      });
    } catch (err) {
      setError('Failed to update like: ' + err.message);
    }
  };

  return (
    <div className="max-w-2xl mx-auto p-4">
      <div className="mb-8">
        <h2 className="text-2xl font-bold mb-4">Community Feed</h2>
        
        {/* Create Post Form */}
        <form onSubmit={handleSubmit} className="mb-6">
          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
              {error}
            </div>
          )}
          <div className="mb-4">
            <textarea
              value={newPost}
              onChange={(e) => setNewPost(e.target.value)}
              placeholder="Share your thoughts..."
              className="w-full px-3 py-2 border rounded-lg focus:outline-none focus:border-blue-500"
              rows="3"
              required
            />
          </div>
          <button
            type="submit"
            disabled={loading}
            className="bg-blue-500 text-white py-2 px-4 rounded-lg hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 disabled:opacity-50"
          >
            {loading ? 'Posting...' : 'Post'}
          </button>
        </form>

        {/* Posts Feed */}
        <div className="space-y-6">
          {posts.map((post) => (
            <div
              key={post.id}
              className="bg-white p-4 rounded-xl shadow-lg border border-gray-100 hover:shadow-xl transition-shadow duration-200"
            >
              <div className="flex items-center mb-3">
                <div className="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center mr-3">
                  <span className="text-gray-600 font-bold">
                    {post.authorName.charAt(0).toUpperCase()}
                  </span>
                </div>
                <div>
                  <div className="font-bold">{post.authorName}</div>
                  <div className="text-gray-500 text-xs">
                    {new Date(post.createdAt).toLocaleString()}
                  </div>
                </div>
              </div>
              <p className="text-gray-800 mb-4">{post.content}</p>
              <div className="flex items-center space-x-4 text-gray-500 border-t pt-3">
                <button
                  className="flex items-center space-x-1 hover:text-red-500"
                  onClick={() => handleLike(post.id, post.likes || 0)}
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                  </svg>
                  <span>{post.likes}</span>
                </button>
                <div className="flex items-center space-x-1 hover:text-blue-500">
                  <svg
                    className="w-5 h-5"
                    fill={post.likedBy?.includes(auth.currentUser?.uid) ? "currentColor" : "none"}
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                  </svg>
                  <span>{post.commentCount || 0}</span>
                </div>
              </div>

              {/* Comments section */}
              <div className="mt-4 pl-4 border-l-2 border-gray-200">
                {comments[post.id]?.map(comment => (
                  <div key={comment.id} className="mb-3">
                    <div className="flex items-center mb-1">
                      <div className="w-6 h-6 rounded-full bg-gray-200 flex items-center justify-center mr-2">
                        <span className="text-xs text-gray-600 font-bold">
                          {comment.authorName.charAt(0).toUpperCase()}
                        </span>
                      </div>
                      <div className="text-sm font-medium">{comment.authorName}</div>
                    </div>
                    <p className="text-sm text-gray-700 ml-8">{comment.content}</p>
                  </div>
                ))}

                {/* Comment form */}
                <div className="mt-4">
                  <textarea
                    value={newComments[post.id] || ''}
                    onChange={(e) => setNewComments(prev => ({
                      ...prev,
                      [post.id]: e.target.value
                    }))}
                    placeholder="Write a comment..."
                    className="w-full px-3 py-2 text-sm border rounded-lg focus:outline-none focus:border-blue-500"
                    rows="1"
                  />
                  <button
                    onClick={() => handleCommentSubmit(post.id)}
                    className="mt-2 bg-blue-500 text-white text-sm py-1 px-3 rounded-lg hover:bg-blue-600 focus:outline-none"
                  >
                    Post Comment
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
