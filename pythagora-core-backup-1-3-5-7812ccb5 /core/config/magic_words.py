PROBLEM_IDENTIFIED = "PROBLEM_IDENTIFIED"
ADD_LOGS = "ADD_LOGS"
THINKING_LOGS = [
    "<PERSON><PERSON><PERSON><PERSON><PERSON> is crunching the numbers...",
    "<PERSON><PERSON><PERSON><PERSON><PERSON> is deep in thought...",
    "<PERSON><PERSON><PERSON><PERSON><PERSON> is analyzing your request...",
    "<PERSON><PERSON><PERSON><PERSON><PERSON> is brewing up a solution...",
    "<PERSON><PERSON><PERSON><PERSON><PERSON> is putting the pieces together...",
    "Pythagora is working its magic...",
    "<PERSON><PERSON><PERSON><PERSON><PERSON> is crafting the perfect response...",
    "<PERSON><PERSON><PERSON><PERSON><PERSON> is decoding your query...",
    "<PERSON><PERSON><PERSON><PERSON><PERSON> is on the case...",
    "<PERSON><PERSON><PERSON><PERSON><PERSON> is computing an answer...",
    "<PERSON><PERSON><PERSON><PERSON><PERSON> is sorting through the data...",
    "<PERSON><PERSON><PERSON><PERSON><PERSON> is gathering insights...",
    "<PERSON><PERSON><PERSON><PERSON><PERSON> is making connections...",
    "<PERSON><PERSON><PERSON><PERSON><PERSON> is tuning the algorithms...",
    "<PERSON><PERSON><PERSON><PERSON><PERSON> is piecing together the puzzle...",
    "<PERSON><PERSON><PERSON><PERSON><PERSON> is scanning the possibilities...",
    "<PERSON><PERSON><PERSON><PERSON><PERSON> is engineering a response...",
    "<PERSON><PERSON><PERSON><PERSON><PERSON> is building the answer...",
    "<PERSON><PERSON><PERSON><PERSON><PERSON> is mapping out a solution...",
    "<PERSON><PERSON><PERSON><PERSON><PERSON> is figuring this out for you...",
    "<PERSON><PERSON><PERSON><PERSON><PERSON> is thinking hard right now...",
    "<PERSON><PERSON><PERSON><PERSON>a is working for you, so relax!",
    "Pythagora might take some time to figure this out...",
]
GITIGNORE_CONTENT = """# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
dist
dist-ssr
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# SQLite databases, data files
*.db
*.csv

# Keep environment variables out of version control
.env
"""
