"""Add docs column to project_states

Revision ID: b760f66138c0
Revises: f352dbe45751
Create Date: 2024-06-08 10:00:44.222099

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "b760f66138c0"
down_revision: Union[str, None] = "f352dbe45751"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table("project_states", schema=None) as batch_op:
        batch_op.add_column(sa.Column("docs", sa.JSON(), nullable=True))

    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table("project_states", schema=None) as batch_op:
        batch_op.drop_column("docs")

    # ### end Alembic commands ###
