"""Make relevant_files nullable

Revision ID: f352dbe45751
Revises: 0a1bb637fa26
Create Date: 2024-06-04 15:07:40.175466

"""

from typing import Sequence, Union

from alembic import op
from sqlalchemy.dialects import sqlite

# revision identifiers, used by Alembic.
revision: str = "f352dbe45751"
down_revision: Union[str, None] = "0a1bb637fa26"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table("project_states", schema=None) as batch_op:
        batch_op.alter_column("relevant_files", existing_type=sqlite.JSON(), nullable=True)

    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table("project_states", schema=None) as batch_op:
        batch_op.alter_column("relevant_files", existing_type=sqlite.JSON(), nullable=False)

    # ### end Alembic commands ###
