"""refactor specification.template to specification.templates

Revision ID: 08d71952ec2f
Revises: ff891d366761
Create Date: 2024-06-14 18:23:09.070736

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "08d71952ec2f"
down_revision: Union[str, None] = "ff891d366761"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table("specifications", schema=None) as batch_op:
        batch_op.add_column(sa.Column("templates", sa.JSON(), nullable=True))
        batch_op.drop_column("template")

    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table("specifications", schema=None) as batch_op:
        batch_op.add_column(sa.Column("template", sa.VARCHAR(), nullable=True))
        batch_op.drop_column("templates")

    # ### end Alembic commands ###
