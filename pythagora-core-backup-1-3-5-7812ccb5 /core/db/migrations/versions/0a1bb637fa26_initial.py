"""initial

Revision ID: 0a1bb637fa26
Revises:
Create Date: 2024-05-28 09:49:51.582784

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "0a1bb637fa26"
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "file_contents",
        sa.Column("id", sa.String(), nullable=False),
        sa.Column("content", sa.String(), nullable=False),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_file_contents")),
    )
    op.create_table(
        "projects",
        sa.Column("id", sa.Uuid(), nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("created_at", sa.DateTime(), server_default=sa.text("(CURRENT_TIMESTAMP)"), nullable=False),
        sa.Column("folder_name", sa.String(), nullable=False),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_projects")),
    )
    op.create_table(
        "specifications",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("description", sa.String(), nullable=False),
        sa.Column("architecture", sa.String(), nullable=False),
        sa.Column("system_dependencies", sa.JSON(), nullable=False),
        sa.Column("package_dependencies", sa.JSON(), nullable=False),
        sa.Column("template", sa.String(), nullable=True),
        sa.Column("complexity", sa.String(), server_default="hard", nullable=False),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_specifications")),
    )
    op.create_table(
        "branches",
        sa.Column("id", sa.Uuid(), nullable=False),
        sa.Column("project_id", sa.Uuid(), nullable=False),
        sa.Column("created_at", sa.DateTime(), server_default=sa.text("(CURRENT_TIMESTAMP)"), nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.ForeignKeyConstraint(
            ["project_id"], ["projects.id"], name=op.f("fk_branches_project_id_projects"), ondelete="CASCADE"
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_branches")),
    )
    op.create_table(
        "project_states",
        sa.Column("id", sa.Uuid(), nullable=False),
        sa.Column("branch_id", sa.Uuid(), nullable=False),
        sa.Column("prev_state_id", sa.Uuid(), nullable=True),
        sa.Column("specification_id", sa.Integer(), nullable=False),
        sa.Column("created_at", sa.DateTime(), server_default=sa.text("(CURRENT_TIMESTAMP)"), nullable=False),
        sa.Column("step_index", sa.Integer(), server_default="1", nullable=False),
        sa.Column("epics", sa.JSON(), nullable=False),
        sa.Column("tasks", sa.JSON(), nullable=False),
        sa.Column("steps", sa.JSON(), nullable=False),
        sa.Column("iterations", sa.JSON(), nullable=False),
        sa.Column("relevant_files", sa.JSON(), nullable=False),
        sa.Column("modified_files", sa.JSON(), nullable=False),
        sa.Column("run_command", sa.String(), nullable=True),
        sa.Column("action", sa.String(), nullable=True),
        sa.ForeignKeyConstraint(
            ["branch_id"], ["branches.id"], name=op.f("fk_project_states_branch_id_branches"), ondelete="CASCADE"
        ),
        sa.ForeignKeyConstraint(
            ["prev_state_id"],
            ["project_states.id"],
            name=op.f("fk_project_states_prev_state_id_project_states"),
            ondelete="CASCADE",
        ),
        sa.ForeignKeyConstraint(
            ["specification_id"], ["specifications.id"], name=op.f("fk_project_states_specification_id_specifications")
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_project_states")),
        sa.UniqueConstraint("branch_id", "step_index", name=op.f("uq_project_states_branch_id")),
        sa.UniqueConstraint("prev_state_id", name=op.f("uq_project_states_prev_state_id")),
        sqlite_autoincrement=True,
    )
    op.create_table(
        "exec_logs",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("branch_id", sa.Uuid(), nullable=False),
        sa.Column("project_state_id", sa.Uuid(), nullable=True),
        sa.Column("started_at", sa.DateTime(), nullable=False),
        sa.Column("duration", sa.Float(), nullable=False),
        sa.Column("cmd", sa.String(), nullable=False),
        sa.Column("cwd", sa.String(), nullable=False),
        sa.Column("env", sa.JSON(), nullable=False),
        sa.Column("timeout", sa.Float(), nullable=True),
        sa.Column("status_code", sa.Integer(), nullable=True),
        sa.Column("stdout", sa.String(), nullable=False),
        sa.Column("stderr", sa.String(), nullable=False),
        sa.Column("analysis", sa.String(), nullable=False),
        sa.Column("success", sa.Boolean(), nullable=False),
        sa.ForeignKeyConstraint(
            ["branch_id"], ["branches.id"], name=op.f("fk_exec_logs_branch_id_branches"), ondelete="CASCADE"
        ),
        sa.ForeignKeyConstraint(
            ["project_state_id"],
            ["project_states.id"],
            name=op.f("fk_exec_logs_project_state_id_project_states"),
            ondelete="SET NULL",
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_exec_logs")),
    )
    op.create_table(
        "files",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("project_state_id", sa.Uuid(), nullable=False),
        sa.Column("content_id", sa.String(), nullable=False),
        sa.Column("path", sa.String(), nullable=False),
        sa.Column("meta", sa.JSON(), server_default="{}", nullable=False),
        sa.ForeignKeyConstraint(
            ["content_id"], ["file_contents.id"], name=op.f("fk_files_content_id_file_contents"), ondelete="RESTRICT"
        ),
        sa.ForeignKeyConstraint(
            ["project_state_id"],
            ["project_states.id"],
            name=op.f("fk_files_project_state_id_project_states"),
            ondelete="CASCADE",
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_files")),
        sa.UniqueConstraint("project_state_id", "path", name=op.f("uq_files_project_state_id")),
    )
    op.create_table(
        "llm_requests",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("branch_id", sa.Uuid(), nullable=False),
        sa.Column("project_state_id", sa.Uuid(), nullable=True),
        sa.Column("started_at", sa.DateTime(), server_default=sa.text("(CURRENT_TIMESTAMP)"), nullable=False),
        sa.Column("agent", sa.String(), nullable=True),
        sa.Column("provider", sa.String(), nullable=False),
        sa.Column("model", sa.String(), nullable=False),
        sa.Column("temperature", sa.Float(), nullable=False),
        sa.Column("messages", sa.JSON(), nullable=False),
        sa.Column("prompts", sa.JSON(), server_default="[]", nullable=False),
        sa.Column("response", sa.String(), nullable=True),
        sa.Column("prompt_tokens", sa.Integer(), nullable=False),
        sa.Column("completion_tokens", sa.Integer(), nullable=False),
        sa.Column("duration", sa.Float(), nullable=False),
        sa.Column("status", sa.String(), nullable=False),
        sa.Column("error", sa.String(), nullable=True),
        sa.ForeignKeyConstraint(
            ["branch_id"], ["branches.id"], name=op.f("fk_llm_requests_branch_id_branches"), ondelete="CASCADE"
        ),
        sa.ForeignKeyConstraint(
            ["project_state_id"],
            ["project_states.id"],
            name=op.f("fk_llm_requests_project_state_id_project_states"),
            ondelete="SET NULL",
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_llm_requests")),
    )
    op.create_table(
        "user_inputs",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("branch_id", sa.Uuid(), nullable=False),
        sa.Column("project_state_id", sa.Uuid(), nullable=True),
        sa.Column("created_at", sa.DateTime(), server_default=sa.text("(CURRENT_TIMESTAMP)"), nullable=False),
        sa.Column("question", sa.String(), nullable=False),
        sa.Column("answer_text", sa.String(), nullable=True),
        sa.Column("answer_button", sa.String(), nullable=True),
        sa.Column("cancelled", sa.Boolean(), nullable=False),
        sa.ForeignKeyConstraint(
            ["branch_id"], ["branches.id"], name=op.f("fk_user_inputs_branch_id_branches"), ondelete="CASCADE"
        ),
        sa.ForeignKeyConstraint(
            ["project_state_id"],
            ["project_states.id"],
            name=op.f("fk_user_inputs_project_state_id_project_states"),
            ondelete="SET NULL",
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_user_inputs")),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("user_inputs")
    op.drop_table("llm_requests")
    op.drop_table("files")
    op.drop_table("exec_logs")
    op.drop_table("project_states")
    op.drop_table("branches")
    op.drop_table("specifications")
    op.drop_table("projects")
    op.drop_table("file_contents")
    # ### end Alembic commands ###
