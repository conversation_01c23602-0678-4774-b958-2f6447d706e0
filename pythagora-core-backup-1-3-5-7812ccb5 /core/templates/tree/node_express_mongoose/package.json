{"name": "{{ project_name }}", "version": "1.0.0", "description": "", "main": "server.js", "scripts": {"start": "node server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"bcrypt": "^5.1.1", "body-parser": "^1.20.2", "chart.js": "^4.4.1", "connect-flash": "^0.1.1", "csv-writer": "^1.6.0", "dotenv": "^16.4.1", "ejs": "^3.1.9", "express": "^4.18.2", "express-session": "^1.18.0", "connect-mongo": "^5.1.0", "moment": "^2.30.1", "mongoose": "^8.1.1", "axios": "^1.7.7", "openai": "^4.63.0", "@anthropic-ai/sdk": "^0.27.3"}}