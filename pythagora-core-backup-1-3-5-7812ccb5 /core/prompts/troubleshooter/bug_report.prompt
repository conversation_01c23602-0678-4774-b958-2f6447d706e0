You're working on an new app and the user has just been testing it.

{% include "partials/project_details.prompt" %}
{% include "partials/files_list.prompt" %}

{% if user_instructions %}
The user was given instructions on how to test if the app is working correctly. Here are the instructions:
```
{{ user_instructions }}
```
{% endif %}

The user then wrote this feedback:
```
{{ user_feedback }}
```
{% if additional_qa|length > 0 %}
Here are questions and answers that you already asked the user:
```
{% for row in additional_qa %}
Q: {{ row.question }}
A: {{ row.answer }}
{% endfor %}
```
{% endif %}

Your job is to identify if feedback is good enough for you to solve the problem. If not, what information you need to solve the problem. Ask for any information that you need to solve the problem.
If you have enough information don't ask any questions.

When thinking of questions, consider the following:
- After getting answers to your questions, you must be able to solve the problem.
- Ask only crucial questions. Do not ask for information that you do not need to solve the problem.
- Ask least amount of questions to get the most information and to solve the problem.
- Ask only questions from the list provided bellow.
- Ask questions in same order as they are in the list.
- Never repeat same question.

Here is the list of questions you can ask:
"Can you please provide more information on what exactly you mean?"
"Can you please provide logs from the frontend?"
"Can you please provide logs from the backend?"
"What is the expected behavior and what is current behaviour?"
"On what page does the issue happen?"