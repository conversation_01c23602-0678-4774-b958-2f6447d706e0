// Test script to verify appointment booking API
const fetch = require('node-fetch');

const testValidAppointment = async () => {
  try {
    const testData = {
      name: 'Test User',
      phone: '************', // Valid US phone number
      service: 'Adult Haircut',
      date: '2025-05-12',
      time: '12:00 PM',
      notes: 'This is a test booking'
    };

    console.log('Testing valid appointment...');

    console.log('Sending valid appointment data:', testData);
    
    let response = await fetch('http://localhost:3000/send-sms-notification', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(testData)
    });
    
    let result = await response.json();
    console.log('Response:', result);
    
    if (!result.success) {
      throw new Error(`Valid appointment test failed: ${result.message}`);
    }
    console.log('✅ Valid appointment test passed');

    // Test invalid phone number
    console.log('\nTesting invalid phone number...');
    const invalidData = {
      ...testData,
      phone: '12345' // Invalid phone number
    };
    
    console.log('Sending invalid phone number:', invalidData);
    response = await fetch('http://localhost:3000/send-sms-notification', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(invalidData)
    });
    
    result = await response.json();
    console.log('Response:', result);
    
    if (result.success) {
      throw new Error('Invalid phone number test failed - should have rejected');
    }
    console.log('✅ Invalid phone number test passed (correctly rejected)');

  } catch (error) {
    console.error('❌ Test error:', error.message);
    process.exit(1);
  }
};

// Run the tests
testValidAppointment();
