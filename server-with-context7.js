// Enhanced Node.js server with Context7 integration for Barber Brothers Legacy
const express = require('express');
const cors = require('cors');
const twilio = require('twilio');
require('dotenv').config();

// Initialize Express app
const app = express();
app.use(cors());
app.use(express.json());

// Initialize Twilio client with improved error handling
const initTwilioClient = () => {
    try {
        return new twilio(process.env.TWILIO_ACCOUNT_SID, process.env.TWILIO_AUTH_TOKEN, {
            timeout: 60000,  // 60 seconds timeout
            lazyLoading: true // Context7 recommended setting
        });
    } catch (error) {
        console.error('Failed to initialize Twilio client:', error);
        throw new Error('Twilio initialization failed');
    }
};

const client = initTwilioClient();

// Phone number validation helper
const validateAndFormatPhone = (phone) => {
    const phoneRegex = /^\+?1?\d{10}$/;
    let formattedPhone = phone.replace(/\D/g, '');
    
    if (formattedPhone.length === 10) {
        formattedPhone = '+1' + formattedPhone;
    } else if (formattedPhone.length === 11 && formattedPhone.startsWith('1')) {
        formattedPhone = '+' + formattedPhone;
    }
    
    if (!phoneRegex.test(formattedPhone)) {
        throw new Error('Invalid phone number format');
    }
    
    return formattedPhone;
};

// Date validation helper
const validateDate = (date) => {
    const appointmentDate = new Date(date);
    const today = new Date();
    
    if (appointmentDate < today) {
        throw new Error('Appointment date must be in the future');
    }
    
    return appointmentDate.toISOString().split('T')[0];
};

// Appointment booking handler
app.post('/send-sms-notification', async (req, res) => {
    console.log('Received booking request:', req.body);
    
    try {
        const { name, phone, service, date, time, notes = '' } = req.body;

        // Validate required fields
        if (!name || !phone || !service || !date || !time) {
            throw new Error('Missing required fields');
        }

        // Format and validate phone number
        const formattedPhone = validateAndFormatPhone(phone);
        
        // Validate appointment date
        const validatedDate = validateDate(date);

        // Prepare messages with template strings
        const customerMessageBody = `Hi ${name}, your appointment for ${service} has been confirmed for ${validatedDate} at ${time}. See you then! - Barber Brothers Legacy`;
        const barberMessageBody = `New appointment: ${name} for ${service} on ${validatedDate} at ${time}. Customer phone: ${formattedPhone}${notes ? `\nNotes: ${notes}` : ''}`;

        // Send messages with improved error handling
        const [customerMessage, barberMessage] = await Promise.all([
            client.messages.create({
                body: customerMessageBody,
                to: formattedPhone,
                from: process.env.TWILIO_PHONE_NUMBER,
                statusCallback: '/message-status' // Optional: track message status
            }),
            client.messages.create({
                body: barberMessageBody,
                to: process.env.BARBER_PHONE_NUMBER,
                from: process.env.TWILIO_PHONE_NUMBER,
                statusCallback: '/message-status' // Optional: track message status
            })
        ]).catch(error => {
            console.error('SMS sending failed:', error);
            throw new Error('Failed to send SMS notifications');
        });

        console.log('Messages sent successfully:', {
            customerMessageSid: customerMessage.sid,
            barberMessageSid: barberMessage.sid
        });

        res.json({
            success: true,
            message: 'Appointment booked successfully!',
            details: {
                appointmentDate: validatedDate,
                appointmentTime: time,
                service,
                messageStatus: {
                    customer: customerMessage.status,
                    barber: barberMessage.status
                }
            }
        });
    } catch (error) {
        console.error('Booking error:', error);
        
        // Enhanced error response
        res.status(error.status || 500).json({
            success: false,
            message: error.message || 'Failed to book appointment',
            errorCode: error.code || 'UNKNOWN_ERROR',
            userMessage: 'Please try again or call us at (404) 309-1971'
        });
    }
});

// Optional: Message status webhook
app.post('/message-status', (req, res) => {
    const messageSid = req.body.MessageSid;
    const messageStatus = req.body.MessageStatus;
    
    console.log('Message status update:', {
        messageSid,
        status: messageStatus,
        timestamp: new Date().toISOString()
    });
    
    res.sendStatus(200);
});

// Health check endpoint
app.get('/health', (req, res) => {
    res.json({
        status: 'healthy',
        twilioConnection: !!client,
        timestamp: new Date().toISOString()
    });
});

// Error handling middleware
app.use((err, req, res, next) => {
    console.error('Unhandled error:', err);
    res.status(500).json({
        success: false,
        message: 'An unexpected error occurred',
        userMessage: 'Please try again or contact support'
    });
});

const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
    console.log(`Server running on port ${PORT}`);
    console.log('Environment:', {
        nodeEnv: process.env.NODE_ENV,
        twilioConfigured: !!process.env.TWILIO_ACCOUNT_SID,
        timestamp: new Date().toISOString()
    });
}); 