document.addEventListener('DOMContentLoaded', function() {
  const appointmentForm = document.getElementById('appointmentForm');
  
  if (appointmentForm) {
    appointmentForm.addEventListener('submit', async function(e) {
      e.preventDefault();
      
      const submitBtn = document.querySelector('button[type="submit"]');
      const originalBtnText = submitBtn.textContent;
      submitBtn.textContent = 'Booking...';
      submitBtn.disabled = true;
      
      // Add phone number format hint
      const phoneInput = document.getElementById('phone');
      phoneInput.setAttribute('placeholder', 'e.g. ************');
      phoneInput.setAttribute('title', 'Please enter a valid US phone number (10 digits)');
      
      const formData = {
        name: document.getElementById('name').value,
        phone: phoneInput.value,
        service: document.getElementById('service').value,
        date: document.getElementById('date').value,
        time: document.getElementById('time').value,
        notes: document.getElementById('notes')?.value || ''
      };
      
      try {
        // Validate phone number format (US numbers only for now)
        const phoneValue = document.getElementById('phone').value;
        if (!/^(\+1|1)?[\s-]?\(?\d{3}\)?[\s-]?\d{3}[\s-]?\d{4}$/.test(phoneValue)) {
          throw new Error('Please enter a valid US phone number (e.g. ************)');
        }

        // Determine which endpoint to use based on the environment
        const isLocalhost = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
        const endpointUrl = isLocalhost 
          ? 'http://localhost:3000/send-sms-notification' 
          : '/.netlify/functions/send-sms';
          
        const response = await fetch(endpointUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(formData)
        });
        
        const result = await response.json();
        
        if (result.success) {
          showMessage('success', 'Appointment booked successfully! You will receive a confirmation text shortly.');
          appointmentForm.reset();
        } else {
          showMessage('error', result.message || 'Failed to book appointment. Please try again.');
        }
      } catch (error) {
        console.error('Error:', error);
        const errorMessage = error.message.includes('valid US phone number') 
          ? error.message 
          : error.message.includes('Failed to send notification') 
            ? error.message 
            : 'Network error. Please try again or call us at (*************.';
        showMessage('error', errorMessage);
      } finally {
        submitBtn.textContent = originalBtnText;
        submitBtn.disabled = false;
      }
    });
  }
  
  function showMessage(type, message) {
    const messageDiv = document.getElementById('form-message') || createMessageElement();
    messageDiv.className = `message ${type}`;
    messageDiv.textContent = message;
    
    // Auto-hide after 5 seconds
    setTimeout(() => {
      messageDiv.style.opacity = '0';
      setTimeout(() => {
        messageDiv.style.display = 'none';
      }, 500);
    }, 5000);
  }
  
  function createMessageElement() {
    const messageDiv = document.createElement('div');
    messageDiv.id = 'form-message';
    appointmentForm.parentNode.insertBefore(messageDiv, appointmentForm);
    return messageDiv;
  }
});
