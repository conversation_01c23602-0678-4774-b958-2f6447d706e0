/* Global Styles */
body {
    font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
}

/* Hero Section */
.hero {
    background: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)), url('../images/hero-bg.jpg');
    background-size: cover;
    background-position: center;
    color: white;
    padding: 100px 0;
    text-align: center;
}

.hero h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.hero p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
}

/* Services Section */
.services .card {
    transition: transform 0.3s ease;
    border: none;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.services .card:hover {
    transform: translateY(-5px);
}

.price {
    font-size: 1.5rem;
    font-weight: bold;
    color: #007bff;
}

/* About Section */
.about img {
    max-width: 100%;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Appointment Section */
.appointment {
    background-color: #f8f9fa;
}

.appointment form {
    background: white;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Form Message Styles */
.message {
    padding: 10px 15px;
    margin-bottom: 20px;
    border-radius: 4px;
    display: block;
}

.success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Footer */
footer {
    margin-top: 2rem;
}

.social-icons a {
    font-size: 1.5rem;
    transition: color 0.3s;
}

.social-icons a:hover {
    color: #007bff !important;
}