<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Button Test - Barber Brothers Legacy</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <style>
        body {
            background-color: #121212;
            color: white;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #333;
            border-radius: 10px;
        }
        .success {
            color: #28a745;
        }
        .error {
            color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Barber Brothers Legacy - Button Functionality Test</h1>
        
        <div class="test-section">
            <h2>Service Modal Tests</h2>
            <div id="test-results"></div>
            
            <button class="btn btn-primary" onclick="testServiceModals()">Test Service Modals</button>
            <button class="btn btn-secondary" onclick="testPriceCalculation()">Test Price Calculation</button>
            <button class="btn btn-success" onclick="testBookNowButtons()">Test Book Now Buttons</button>
        </div>
        
        <div class="test-section">
            <h2>Manual Test Links</h2>
            <p>Click these buttons to manually test the service modals:</p>
            <button class="btn btn-danger me-2" data-bs-toggle="modal" data-bs-target="#adultHaircutModal">Adult Haircut Modal</button>
            <button class="btn btn-danger me-2" data-bs-toggle="modal" data-bs-target="#kidsHaircutModal">Kids Haircut Modal</button>
            <button class="btn btn-danger me-2" data-bs-toggle="modal" data-bs-target="#fullServiceModal">Full Service Modal</button>
        </div>
        
        <div class="test-section">
            <h2>Go to Main Site</h2>
            <a href="index.html" class="btn btn-success">Open Main Website</a>
        </div>
    </div>

    <!-- Include the modals from the main site -->
    <!-- Adult Haircut Modal -->
    <div class="modal fade" id="adultHaircutModal" tabindex="-1" aria-labelledby="adultHaircutModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="adultHaircutModalLabel">ADULT HAIRCUTS SERVICE DETAILS</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="service-details">
                        <div class="service-selection-card">
                            <div class="service-options">
                                <!-- Base Service -->
                                <div class="service-option mb-4">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" id="baseService" checked disabled>
                                        <label class="form-check-label" for="baseService">
                                            <span class="service-name">Standard Haircut</span>
                                            <span class="service-description">Professional haircut with precision detail</span>
                                            <span class="price-tag">$40</span>
                                        </label>
                                    </div>
                                </div>

                                <!-- Additional Services -->
                                <div class="service-option mb-4">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input service-addon" id="hairWash" data-price="5">
                                        <label class="form-check-label" for="hairWash">
                                            <span class="service-name">Hair Wash & Scalp Refresh</span>
                                            <span class="service-description">Relaxing shampoo and conditioning treatment</span>
                                            <span class="price-tag">+$5</span>
                                        </label>
                                    </div>
                                </div>

                                <div class="service-option mb-4">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input service-addon" id="beardTrim" data-price="5">
                                        <label class="form-check-label" for="beardTrim">
                                            <span class="service-name">Beard Trim & Line-Up</span>
                                            <span class="service-description">Clean and precise beard shaping</span>
                                            <span class="price-tag">+$5</span>
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="total-price mt-4">
                                <h6>Total Price: <span id="totalPrice">$40</span></h6>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-danger book-now-btn" data-service-type="adult" data-bs-dismiss="modal">Book Now</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Kids Haircut Modal -->
    <div class="modal fade" id="kidsHaircutModal" tabindex="-1" aria-labelledby="kidsHaircutModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="kidsHaircutModalLabel">KIDS HAIRCUTS SERVICE DETAILS</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="service-details">
                        <div class="service-selection-card">
                            <div class="service-options">
                                <!-- Base Service -->
                                <div class="service-option mb-4">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" id="kidsBaseService" checked disabled>
                                        <label class="form-check-label" for="kidsBaseService">
                                            <span class="service-name">Basic Kids Cut</span>
                                            <span class="service-description">Basic haircut with light styling</span>
                                            <span class="price-tag">$30</span>
                                        </label>
                                    </div>
                                </div>

                                <!-- Additional Services -->
                                <div class="service-option mb-4">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input kids-service-addon" id="kidsHairWash" data-price="5">
                                        <label class="form-check-label" for="kidsHairWash">
                                            <span class="service-name">Hair Wash & Conditioning</span>
                                            <span class="service-description">Gentle cleansing and conditioning treatment</span>
                                            <span class="price-tag">+$5</span>
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="total-price mt-4">
                                <h6>Total Price: <span id="kidsTotalPrice" style="color: #dc3545; font-weight: bold; font-size: 1.3em;">$30</span></h6>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-danger book-now-btn" data-service-type="kids" data-bs-dismiss="modal">Book Now</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Full Service Modal -->
    <div class="modal fade" id="fullServiceModal" tabindex="-1" aria-labelledby="fullServiceModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="fullServiceModalLabel">Full Service Experience Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="service-details">
                        <div class="service-selection-card">
                            <div class="service-options">
                                <!-- Base Service -->
                                <div class="service-option mb-4">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" id="fullServiceBase" checked disabled>
                                        <label class="form-check-label" for="fullServiceBase">
                                            <span class="service-name">Premium Full Service Package</span>
                                            <span class="service-description">Complete grooming experience</span>
                                            <span class="price-tag">$60</span>
                                        </label>
                                    </div>
                                </div>

                                <!-- Additional Premium Services -->
                                <div class="service-option mb-4">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input service-addon" id="fullPremiumColor" data-price="20">
                                        <label class="form-check-label" for="fullPremiumColor">
                                            <span class="service-name">Premium Color Treatment</span>
                                            <span class="service-description">Professional hair/beard coloring with premium products</span>
                                            <span class="price-tag">+$20</span>
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="total-price mt-4">
                                <h6>Total Price: <span id="fullServiceTotalPrice">$60</span></h6>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-danger book-now-btn" data-service-type="full" data-bs-dismiss="modal">Book Now</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/service-calculator.js"></script>
    
    <script>
        function testServiceModals() {
            const results = document.getElementById('test-results');
            let output = '<h4>Service Modal Test Results:</h4>';
            
            // Test if modals exist
            const adultModal = document.getElementById('adultHaircutModal');
            const kidsModal = document.getElementById('kidsHaircutModal');
            const fullModal = document.getElementById('fullServiceModal');
            
            output += adultModal ? '<p class="success">✓ Adult Haircut Modal exists</p>' : '<p class="error">✗ Adult Haircut Modal missing</p>';
            output += kidsModal ? '<p class="success">✓ Kids Haircut Modal exists</p>' : '<p class="error">✗ Kids Haircut Modal missing</p>';
            output += fullModal ? '<p class="success">✓ Full Service Modal exists</p>' : '<p class="error">✗ Full Service Modal missing</p>';
            
            results.innerHTML = output;
        }
        
        function testPriceCalculation() {
            const results = document.getElementById('test-results');
            let output = '<h4>Price Calculation Test Results:</h4>';
            
            // Test adult haircut price calculation
            const adultCheckbox = document.getElementById('hairWash');
            const adultTotal = document.getElementById('totalPrice');
            
            if (adultCheckbox && adultTotal) {
                const initialPrice = adultTotal.textContent;
                adultCheckbox.checked = true;
                adultCheckbox.dispatchEvent(new Event('change'));
                
                setTimeout(() => {
                    const newPrice = adultTotal.textContent;
                    if (newPrice !== initialPrice) {
                        output += '<p class="success">✓ Adult haircut price calculation working</p>';
                    } else {
                        output += '<p class="error">✗ Adult haircut price calculation not working</p>';
                    }
                    
                    adultCheckbox.checked = false;
                    adultCheckbox.dispatchEvent(new Event('change'));
                    
                    results.innerHTML = output;
                }, 100);
            } else {
                output += '<p class="error">✗ Adult haircut elements not found</p>';
                results.innerHTML = output;
            }
        }
        
        function testBookNowButtons() {
            const results = document.getElementById('test-results');
            let output = '<h4>Book Now Button Test Results:</h4>';
            
            const bookNowButtons = document.querySelectorAll('.book-now-btn');
            output += `<p class="success">✓ Found ${bookNowButtons.length} Book Now buttons</p>`;
            
            bookNowButtons.forEach((button, index) => {
                const serviceType = button.dataset.serviceType;
                output += serviceType ? 
                    `<p class="success">✓ Button ${index + 1} has service type: ${serviceType}</p>` : 
                    `<p class="error">✗ Button ${index + 1} missing service type</p>`;
            });
            
            results.innerHTML = output;
        }
    </script>
</body>
</html>
