import { initializeApp } from "https://www.gstatic.com/firebasejs/9.6.1/firebase-app.js";
// If you are using Firebase Analytics, uncomment the line below
// import { getAnalytics } from "https://www.gstatic.com/firebasejs/9.6.1/firebase-analytics.js";

const firebaseConfig = {
  apiKey: "AIzaSyAHqpO8PPXmZIfype7dsViz3chKcmLdmpY", // Replace with your actual API Key
  authDomain: "barber-brothers-legacy.firebaseapp.com", // Replace YOUR_PROJECT_ID with your actual Project ID
  projectId: "barber-brothers-legacy", // Replace with your actual Project ID
  storageBucket: "barber-brothers-legacy.appspot.com", // Replace YOUR_PROJECT_ID with your actual Project ID
  messagingSenderId: "YOUR_MESSAGING_SENDER_ID", // Replace with your actual Messaging Sender ID
  appId: "1:946338896038:web:d65f5bef7973127d1abc67", // Your App ID
  measurementId: "YOUR_MEASUREMENT_ID" // Replace with your actual Measurement ID (if using Analytics)
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// If you are using Firebase Analytics, initialize it and export it
// const analytics = getAnalytics(app);

// Export the app instance to be used in other parts of your application
export { app };
