// Barber Brothers Media - Social Feed Functionality
document.addEventListener('DOMContentLoaded', function() {
    // DOM Elements
    const followButton = document.querySelector('.social-profile-header .btn-danger');
    const postInput = document.querySelector('.create-post input[type="text"]');
    const postButton = document.querySelector('.create-post .btn-danger');
    const commentInputs = document.querySelectorAll('.add-comment input');
    const likeButtons = document.querySelectorAll('.post-actions .btn-link:first-child');
    const commentButtons = document.querySelectorAll('.post-actions .btn-link:nth-child(2)');
    const shareButtons = document.querySelectorAll('.post-actions .btn-link:last-child');
    
    // Follow button functionality
    if (followButton) {
        followButton.addEventListener('click', function() {
            if (this.textContent === 'Follow') {
                this.textContent = 'Following';
                this.classList.remove('btn-danger');
                this.classList.add('btn-outline-danger');
                
                // Update follower count - in a real app, this would be a server call
                const followerCount = document.querySelector('.social-stats span:nth-child(2) strong');
                if (followerCount) {
                    const currentCount = parseFloat(followerCount.textContent);
                    followerCount.textContent = (currentCount + 0.1).toFixed(1) + 'K';
                }
                
                // Show toast notification
                showToast('You are now following Barber Brothers Media!');
            } else {
                this.textContent = 'Follow';
                this.classList.remove('btn-outline-danger');
                this.classList.add('btn-danger');
                
                // Update follower count
                const followerCount = document.querySelector('.social-stats span:nth-child(2) strong');
                if (followerCount) {
                    const currentCount = parseFloat(followerCount.textContent);
                    followerCount.textContent = (currentCount - 0.1).toFixed(1) + 'K';
                }
            }
        });
    }
    
    // Post creation functionality
    if (postButton && postInput) {
        postButton.addEventListener('click', function() {
            const postContent = postInput.value.trim();
            if (postContent) {
                createNewPost(postContent);
                postInput.value = '';
                
                // Show toast notification
                showToast('Your post has been published!');
            }
        });
        
        // Allow Enter key to submit post
        postInput.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                postButton.click();
            }
        });
    }
    
    // Like button functionality
    likeButtons.forEach(button => {
        button.addEventListener('click', function() {
            const postContainer = this.closest('.social-post');
            const likeCount = postContainer.querySelector('.post-stats span:first-child');
            const icon = this.querySelector('i');
            
            if (icon.classList.contains('far')) {
                // Like the post
                icon.classList.remove('far');
                icon.classList.add('fas');
                icon.classList.add('text-danger');
                
                // Update like count
                if (likeCount) {
                    const currentLikes = parseInt(likeCount.textContent.match(/\d+/)[0]);
                    likeCount.innerHTML = `<i class="fas fa-heart text-danger me-1"></i> ${currentLikes + 1}`;
                }
            } else {
                // Unlike the post
                icon.classList.remove('fas');
                icon.classList.remove('text-danger');
                icon.classList.add('far');
                
                // Update like count
                if (likeCount) {
                    const currentLikes = parseInt(likeCount.textContent.match(/\d+/)[0]);
                    likeCount.innerHTML = `<i class="fas fa-heart text-danger me-1"></i> ${currentLikes - 1}`;
                }
            }
        });
    });
    
    // Comment input functionality
    commentInputs.forEach(input => {
        input.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                const commentText = this.value.trim();
                if (commentText) {
                    const commentsContainer = this.closest('.add-comment').previousElementSibling;
                    if (commentsContainer && commentsContainer.classList.contains('comments')) {
                        addNewComment(commentsContainer, commentText);
                        this.value = '';
                        
                        // Update comment count
                        const postContainer = this.closest('.social-post');
                        const commentCount = postContainer.querySelector('.post-stats span:nth-child(2)');
                        if (commentCount) {
                            const currentComments = parseInt(commentCount.textContent.match(/\d+/)[0]);
                            commentCount.innerHTML = `<i class="fas fa-comment me-1"></i> ${currentComments + 1}`;
                        }
                    } else {
                        // Create comments section if it doesn't exist
                        const postBody = this.closest('.card-body');
                        const newCommentsSection = document.createElement('div');
                        newCommentsSection.className = 'comments mt-3';
                        
                        const addCommentDiv = this.closest('.add-comment');
                        postBody.insertBefore(newCommentsSection, addCommentDiv);
                        
                        addNewComment(newCommentsSection, commentText);
                        this.value = '';
                        
                        // Update comment count
                        const postContainer = this.closest('.social-post');
                        const commentCount = postContainer.querySelector('.post-stats span:nth-child(2)');
                        if (commentCount) {
                            const currentComments = parseInt(commentCount.textContent.match(/\d+/)[0]);
                            commentCount.innerHTML = `<i class="fas fa-comment me-1"></i> ${currentComments + 1}`;
                        }
                    }
                }
            }
        });
    });
    
    // Function to create a new comment
    function addNewComment(container, text) {
        const newComment = document.createElement('div');
        newComment.className = 'comment d-flex mb-3';
        
        // In a real application, you would use the current user's profile picture
        newComment.innerHTML = `
            <img src="images/time.jpeg" alt="You" class="user-avatar-sm me-2" width="30" height="30">
            <div class="comment-content bg-secondary bg-opacity-25 p-2 rounded flex-grow-1">
                <h6 class="mb-0 small">You</h6>
                <p class="small mb-1">${text}</p>
                <div class="comment-actions small">
                    <span class="text-muted">Just now</span>
                    <a href="#" class="text-white ms-2">Like</a>
                    <a href="#" class="text-white ms-2">Reply</a>
                </div>
            </div>
        `;
        
        container.appendChild(newComment);
    }
    
    // Function to create a new post
    function createNewPost(content) {
        const postsContainer = document.querySelector('.posts-feed');
        if (!postsContainer) return;
        
        const newPost = document.createElement('div');
        newPost.className = 'card social-post mb-4 bg-black text-white';
        
        // Format the current date for the post
        const now = new Date();
        
        newPost.innerHTML = `
            <div class="card-header post-header border-0 bg-black">
                <div class="d-flex align-items-center">
                    <img src="images/time.jpeg" alt="You" class="user-avatar me-2" width="40" height="40">
                    <div>
                        <h6 class="mb-0">You</h6>
                        <small class="text-muted">Just now</small>
                    </div>
                    <div class="dropdown ms-auto">
                        <button class="btn btn-link text-white" data-bs-toggle="dropdown">
                            <i class="fas fa-ellipsis-v"></i>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-dark">
                            <li><a class="dropdown-item" href="#">Edit Post</a></li>
                            <li><a class="dropdown-item" href="#">Delete</a></li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="card-body pb-2">
                <p class="card-text">${content}</p>
                <div class="post-stats d-flex align-items-center text-muted mb-3">
                    <span><i class="fas fa-heart text-danger me-1"></i> 0</span>
                    <span class="ms-3"><i class="fas fa-comment me-1"></i> 0</span>
                    <span class="ms-3"><i class="fas fa-share me-1"></i> 0</span>
                </div>
                <hr class="my-2">
                <div class="post-actions d-flex">
                    <button class="btn btn-link text-white flex-grow-1"><i class="far fa-heart me-1"></i> Like</button>
                    <button class="btn btn-link text-white flex-grow-1"><i class="far fa-comment me-1"></i> Comment</button>
                    <button class="btn btn-link text-white flex-grow-1"><i class="far fa-share-square me-1"></i> Share</button>
                </div>
                <!-- Add comment input -->
                <div class="add-comment d-flex mt-3">
                    <img src="images/time.jpeg" alt="User" class="user-avatar-sm me-2" width="30" height="30">
                    <input type="text" class="form-control form-control-sm bg-dark text-white" placeholder="Write a comment...">
                </div>
            </div>
        `;
        
        // Insert the new post at the beginning of the feed
        postsContainer.insertBefore(newPost, postsContainer.firstChild);
        
        // Add event listeners to the new post
        const likeButton = newPost.querySelector('.post-actions .btn-link:first-child');
        if (likeButton) {
            likeButton.addEventListener('click', function() {
                const icon = this.querySelector('i');
                const likeCount = newPost.querySelector('.post-stats span:first-child');
                
                if (icon.classList.contains('far')) {
                    icon.classList.remove('far');
                    icon.classList.add('fas');
                    icon.classList.add('text-danger');
                    
                    if (likeCount) {
                        const currentLikes = parseInt(likeCount.textContent.match(/\d+/)[0]);
                        likeCount.innerHTML = `<i class="fas fa-heart text-danger me-1"></i> ${currentLikes + 1}`;
                    }
                } else {
                    icon.classList.remove('fas');
                    icon.classList.remove('text-danger');
                    icon.classList.add('far');
                    
                    if (likeCount) {
                        const currentLikes = parseInt(likeCount.textContent.match(/\d+/)[0]);
                        likeCount.innerHTML = `<i class="fas fa-heart text-danger me-1"></i> ${currentLikes - 1}`;
                    }
                }
            });
        }
        
        // Add event listener to new comment input
        const commentInput = newPost.querySelector('.add-comment input');
        if (commentInput) {
            commentInput.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    const commentText = this.value.trim();
                    if (commentText) {
                        // Create comments section if it doesn't exist
                        let commentsContainer = this.closest('.add-comment').previousElementSibling;
                        
                        if (!commentsContainer || !commentsContainer.classList.contains('comments')) {
                            const postBody = this.closest('.card-body');
                            const newCommentsSection = document.createElement('div');
                            newCommentsSection.className = 'comments mt-3';
                            
                            const addCommentDiv = this.closest('.add-comment');
                            const actionsDiv = addCommentDiv.previousElementSibling;
                            
                            postBody.insertBefore(newCommentsSection, actionsDiv.nextSibling);
                            commentsContainer = newCommentsSection;
                        }
                        
                        addNewComment(commentsContainer, commentText);
                        this.value = '';
                        
                        // Update comment count
                        const commentCount = newPost.querySelector('.post-stats span:nth-child(2)');
                        if (commentCount) {
                            const currentComments = parseInt(commentCount.textContent.match(/\d+/)[0]);
                            commentCount.innerHTML = `<i class="fas fa-comment me-1"></i> ${currentComments + 1}`;
                        }
                    }
                }
            });
        }
    }
    
    // Create a toast notification function
    function showToast(message) {
        // Check if toast container exists, create if not
        let toastContainer = document.querySelector('.toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
            document.body.appendChild(toastContainer);
        }
        
        // Create toast element
        const toastId = 'toast-' + Date.now();
        const toastEl = document.createElement('div');
        toastEl.className = 'toast';
        toastEl.id = toastId;
        toastEl.setAttribute('role', 'alert');
        toastEl.setAttribute('aria-live', 'assertive');
        toastEl.setAttribute('aria-atomic', 'true');
        
        toastEl.innerHTML = `
            <div class="toast-header">
                <strong class="me-auto">Barber Brothers Media</strong>
                <small>Just now</small>
                <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
            <div class="toast-body">
                ${message}
            </div>
        `;
        
        // Add to container
        toastContainer.appendChild(toastEl);
        
        // Initialize and show the toast
        const toast = new bootstrap.Toast(toastEl);
        toast.show();
        
        // Remove after it's hidden
        toastEl.addEventListener('hidden.bs.toast', function() {
            toastEl.remove();
        });
    }
});
