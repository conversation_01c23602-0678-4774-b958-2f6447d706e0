// Performance monitoring for Barber Brothers Legacy website
// This script tracks image loading performance and provides insights

class PerformanceMonitor {
    constructor() {
        this.imageLoadTimes = new Map();
        this.totalImagesLoaded = 0;
        this.totalLoadTime = 0;
        this.startTime = performance.now();
        
        this.init();
    }
    
    init() {
        // Monitor page load performance
        this.monitorPageLoad();
        
        // Monitor image loading
        this.monitorImageLoading();
        
        // Monitor network conditions
        this.monitorNetworkConditions();
        
        console.log('🚀 Performance monitoring initialized');
    }
    
    monitorPageLoad() {
        window.addEventListener('load', () => {
            const loadTime = performance.now() - this.startTime;
            console.log(`📊 Page fully loaded in ${loadTime.toFixed(2)}ms`);
            
            // Get navigation timing data
            if (performance.getEntriesByType) {
                const navTiming = performance.getEntriesByType('navigation')[0];
                if (navTiming) {
                    console.log('📈 Navigation Timing:');
                    console.log(`   DNS Lookup: ${(navTiming.domainLookupEnd - navTiming.domainLookupStart).toFixed(2)}ms`);
                    console.log(`   TCP Connect: ${(navTiming.connectEnd - navTiming.connectStart).toFixed(2)}ms`);
                    console.log(`   Request: ${(navTiming.responseStart - navTiming.requestStart).toFixed(2)}ms`);
                    console.log(`   Response: ${(navTiming.responseEnd - navTiming.responseStart).toFixed(2)}ms`);
                    console.log(`   DOM Processing: ${(navTiming.domComplete - navTiming.domLoading).toFixed(2)}ms`);
                }
            }
        });
    }
    
    monitorImageLoading() {
        // Monitor all images on the page
        const images = document.querySelectorAll('img');
        
        images.forEach((img, index) => {
            const startTime = performance.now();
            
            // Track when image starts loading
            if (img.complete) {
                // Image already loaded
                this.recordImageLoad(img.src, 0, 'cached');
            } else {
                // Image still loading
                img.addEventListener('load', () => {
                    const loadTime = performance.now() - startTime;
                    this.recordImageLoad(img.src, loadTime, 'network');
                });
                
                img.addEventListener('error', () => {
                    console.error(`❌ Failed to load image: ${img.src}`);
                });
            }
        });
    }
    
    recordImageLoad(src, loadTime, source) {
        this.totalImagesLoaded++;
        this.totalLoadTime += loadTime;
        
        const filename = src.split('/').pop();
        this.imageLoadTimes.set(filename, {
            loadTime: loadTime,
            source: source,
            timestamp: Date.now()
        });
        
        if (source === 'network') {
            console.log(`🖼️ Image loaded: ${filename} (${loadTime.toFixed(2)}ms)`);
        } else {
            console.log(`💾 Image from cache: ${filename}`);
        }
        
        // Check if this is a gallery image
        if (filename.includes('IMG_') || filename.includes('d1.jpg')) {
            this.trackGalleryImagePerformance(filename, loadTime, source);
        }
    }
    
    trackGalleryImagePerformance(filename, loadTime, source) {
        const performance = {
            excellent: loadTime < 500,
            good: loadTime < 1000,
            fair: loadTime < 2000,
            poor: loadTime >= 2000
        };
        
        let rating = 'poor';
        if (performance.excellent) rating = 'excellent';
        else if (performance.good) rating = 'good';
        else if (performance.fair) rating = 'fair';
        
        const emoji = {
            excellent: '🚀',
            good: '✅',
            fair: '⚠️',
            poor: '🐌'
        };
        
        console.log(`${emoji[rating]} Gallery image ${filename}: ${loadTime.toFixed(2)}ms (${rating})`);
    }
    
    monitorNetworkConditions() {
        // Check if Network Information API is available
        if ('connection' in navigator) {
            const connection = navigator.connection;
            console.log('🌐 Network Information:');
            console.log(`   Connection Type: ${connection.effectiveType || 'unknown'}`);
            console.log(`   Downlink: ${connection.downlink || 'unknown'} Mbps`);
            console.log(`   RTT: ${connection.rtt || 'unknown'}ms`);
            
            // Adjust loading strategy based on connection
            if (connection.effectiveType === 'slow-2g' || connection.effectiveType === '2g') {
                console.log('📱 Slow connection detected - consider showing lower quality images');
            }
        }
    }
    
    getPerformanceReport() {
        const avgLoadTime = this.totalImagesLoaded > 0 ? 
            this.totalLoadTime / this.totalImagesLoaded : 0;
        
        return {
            totalImages: this.totalImagesLoaded,
            averageLoadTime: avgLoadTime,
            imageDetails: Array.from(this.imageLoadTimes.entries()),
            recommendations: this.getRecommendations()
        };
    }
    
    getRecommendations() {
        const recommendations = [];
        
        // Check for slow loading images
        this.imageLoadTimes.forEach((data, filename) => {
            if (data.loadTime > 2000) {
                recommendations.push(`Consider further optimizing ${filename} (${data.loadTime.toFixed(2)}ms)`);
            }
        });
        
        // Check total load time
        if (this.totalLoadTime > 5000) {
            recommendations.push('Consider implementing progressive image loading');
        }
        
        return recommendations;
    }
    
    logSummary() {
        const report = this.getPerformanceReport();
        
        console.log('\n📊 PERFORMANCE SUMMARY');
        console.log('========================');
        console.log(`Total Images Loaded: ${report.totalImages}`);
        console.log(`Average Load Time: ${report.averageLoadTime.toFixed(2)}ms`);
        
        if (report.recommendations.length > 0) {
            console.log('\n💡 Recommendations:');
            report.recommendations.forEach(rec => console.log(`   • ${rec}`));
        } else {
            console.log('\n✅ All images loading efficiently!');
        }
        
        console.log('========================\n');
    }
}

// Initialize performance monitoring when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    const monitor = new PerformanceMonitor();
    
    // Log summary after all images have had time to load
    setTimeout(() => {
        monitor.logSummary();
    }, 5000);
    
    // Make monitor available globally for debugging
    window.performanceMonitor = monitor;
});
