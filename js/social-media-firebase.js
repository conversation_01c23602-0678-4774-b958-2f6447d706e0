// Barber Brothers Media - Firebase Integration
document.addEventListener('DOMContentLoaded', function() {
    // Check if Firebase is initialized
    if (typeof firebase === 'undefined') {
        console.error('Firebase is not initialized!');
        return;
    }

    // Firebase services
    const auth = firebase.auth();
    const db = firebase.firestore();
    const storage = firebase.storage();
    
    // DOM elements
    const loginSection = document.getElementById('login-section');
    const postSection = document.getElementById('post-section');
    const feedSection = document.getElementById('feed-section');
    const followButton = document.querySelector('.social-profile-header .btn-danger');
    const postButton = document.querySelector('.create-post .btn-danger');
    const postInput = document.querySelector('.create-post input[type="text"]');
    
    // User state
    let currentUser = null;
    
    // Check authentication state
    auth.onAuthStateChanged(function(user) {
        if (user) {
            // User is signed in
            currentUser = user;
            updateUIForSignedInUser(user);
            loadPosts();
        } else {
            // No user is signed in
            currentUser = null;
            updateUIForSignedOutUser();
        }
    });
    
    // Handle Google Sign In
    if (document.getElementById('google-login')) {
        document.getElementById('google-login').addEventListener('click', function() {
            const provider = new firebase.auth.GoogleAuthProvider();
            auth.signInWithPopup(provider)
                .catch(error => {
                    console.error("Error during sign in:", error);
                    showToast("Sign in failed. Please try again.");
                });
        });
    }
    
    // Handle Sign Out
    document.body.addEventListener('click', function(e) {
        if (e.target && e.target.id === 'sign-out-button') {
            auth.signOut()
                .then(() => {
                    showToast("Signed out successfully");
                })
                .catch(error => {
                    console.error("Error signing out:", error);
                });
        }
    });
    
    // Handle post submission
    if (postButton && postInput) {
        postButton.addEventListener('click', createPost);
    }
    
    // Handle follow/unfollow
    if (followButton) {
        followButton.addEventListener('click', function() {
            if (!currentUser) {
                showToast("Please sign in to follow");
                return;
            }
            
            if (this.textContent === 'Follow') {
                followChannel()
                    .then(() => {
                        this.textContent = 'Following';
                        this.classList.remove('btn-danger');
                        this.classList.add('btn-outline-danger');
                        updateFollowerCount(1);
                        showToast("You are now following Barber Brothers Media!");
                    })
                    .catch(error => {
                        console.error("Error following:", error);
                        showToast("Failed to follow. Please try again.");
                    });
            } else {
                unfollowChannel()
                    .then(() => {
                        this.textContent = 'Follow';
                        this.classList.remove('btn-outline-danger');
                        this.classList.add('btn-danger');
                        updateFollowerCount(-1);
                        showToast("You have unfollowed Barber Brothers Media");
                    })
                    .catch(error => {
                        console.error("Error unfollowing:", error);
                        showToast("Failed to unfollow. Please try again.");
                    });
            }
        });
    }
    
    // Handle like/unlike for existing posts
    document.body.addEventListener('click', function(e) {
        if (!currentUser) return;
        
        // Handle likes
        if (e.target && (e.target.classList.contains('far') || e.target.classList.contains('fas') || e.target.textContent.includes('Like'))) {
            const likeButton = e.target.closest('.btn-link');
            if (likeButton && likeButton.textContent.includes('Like')) {
                const postElement = likeButton.closest('.social-post');
                if (postElement) {
                    const postId = postElement.dataset.postId;
                    if (postId) {
                        toggleLike(postId, postElement);
                    }
                }
            }
        }
    });
    
    // Handle comment submission for existing comments
    document.body.addEventListener('keydown', function(e) {
        if (!currentUser) return;
        
        if (e.target && e.target.classList.contains('form-control-sm') && e.target.placeholder === 'Write a comment...') {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                const commentText = e.target.value.trim();
                if (commentText) {
                    const postElement = e.target.closest('.social-post');
                    if (postElement) {
                        const postId = postElement.dataset.postId;
                        if (postId) {
                            addComment(postId, commentText, postElement)
                                .then(() => {
                                    e.target.value = '';
                                })
                                .catch(error => {
                                    console.error("Error adding comment:", error);
                                    showToast("Failed to add comment. Please try again.");
                                });
                        }
                    }
                }
            }
        }
    });
    
    // Functions to interact with Firebase
    
    // Create a new post
    function createPost() {
        if (!currentUser) {
            showToast("Please sign in to post");
            return;
        }
        
        const content = postInput.value.trim();
        if (!content) {
            showToast("Please add some content to your post");
            return;
        }
        
        const newPost = {
            content: content,
            author: {
                uid: currentUser.uid,
                displayName: currentUser.displayName || 'Anonymous',
                photoURL: currentUser.photoURL || 'images/time.jpeg'
            },
            likes: 0,
            comments: 0,
            shares: 0,
            timestamp: firebase.firestore.FieldValue.serverTimestamp()
        };
        
        db.collection('posts').add(newPost)
            .then(docRef => {
                postInput.value = '';
                showToast("Your post has been published!");
                
                // Update the UI with the new post
                fetchPost(docRef.id);
            })
            .catch(error => {
                console.error("Error adding post:", error);
                showToast("Failed to publish post. Please try again.");
            });
    }
    
    // Fetch a single post by ID
    function fetchPost(postId) {
        db.collection('posts').doc(postId).get()
            .then(doc => {
                if (doc.exists) {
                    const postData = doc.data();
                    postData.id = doc.id;
                    
                    // Prepend new post to feed
                    const postElement = createPostElement(postData);
                    if (feedSection.firstChild) {
                        feedSection.insertBefore(postElement, feedSection.firstChild);
                    } else {
                        feedSection.appendChild(postElement);
                    }
                }
            })
            .catch(error => {
                console.error("Error fetching post:", error);
            });
    }
    
    // Load posts from Firestore
    function loadPosts() {
        feedSection.innerHTML = '<div class="text-center"><div class="spinner-border text-danger" role="status"></div><p class="mt-2">Loading posts...</p></div>';
        
        db.collection('posts')
            .orderBy('timestamp', 'desc')
            .limit(10)
            .get()
            .then(querySnapshot => {
                feedSection.innerHTML = '';
                
                if (querySnapshot.empty) {
                    feedSection.innerHTML = '<p class="text-center">No posts yet. Be the first to post!</p>';
                    return;
                }
                
                querySnapshot.forEach(doc => {
                    const postData = doc.data();
                    postData.id = doc.id;
                    const postElement = createPostElement(postData);
                    feedSection.appendChild(postElement);
                });
            })
            .catch(error => {
                console.error("Error loading posts:", error);
                feedSection.innerHTML = '<p class="text-center text-danger">Failed to load posts. Please try again later.</p>';
            });
    }
    
    // Toggle like on a post
    function toggleLike(postId, postElement) {
        const likeButton = postElement.querySelector('.post-actions .btn-link:first-child');
        const likeIcon = likeButton.querySelector('i');
        const likeCountElement = postElement.querySelector('.post-stats span:first-child');
        
        // Check if user already liked this post
        db.collection('likes')
            .where('postId', '==', postId)
            .where('userId', '==', currentUser.uid)
            .get()
            .then(querySnapshot => {
                if (querySnapshot.empty) {
                    // User hasn't liked the post, so add like
                    return db.collection('likes').add({
                        postId: postId,
                        userId: currentUser.uid,
                        timestamp: firebase.firestore.FieldValue.serverTimestamp()
                    })
                    .then(() => {
                        return db.collection('posts').doc(postId).update({
                            likes: firebase.firestore.FieldValue.increment(1)
                        });
                    })
                    .then(() => {
                        // Update UI
                        likeIcon.classList.remove('far');
                        likeIcon.classList.add('fas', 'text-danger');
                        
                        if (likeCountElement) {
                            const currentLikes = parseInt(likeCountElement.textContent.match(/\\d+/)[0]);
                            likeCountElement.innerHTML = `<i class="fas fa-heart text-danger me-1"></i> ${currentLikes + 1}`;
                        }
                    });
                } else {
                    // User already liked the post, so remove like
                    const likeDoc = querySnapshot.docs[0];
                    return likeDoc.ref.delete()
                    .then(() => {
                        return db.collection('posts').doc(postId).update({
                            likes: firebase.firestore.FieldValue.increment(-1)
                        });
                    })
                    .then(() => {
                        // Update UI
                        likeIcon.classList.remove('fas', 'text-danger');
                        likeIcon.classList.add('far');
                        
                        if (likeCountElement) {
                            const currentLikes = parseInt(likeCountElement.textContent.match(/\\d+/)[0]);
                            likeCountElement.innerHTML = `<i class="fas fa-heart text-danger me-1"></i> ${currentLikes - 1}`;
                        }
                    });
                }
            })
            .catch(error => {
                console.error("Error toggling like:", error);
                showToast("Failed to like post. Please try again.");
            });
    }
    
    // Add a comment to a post
    function addComment(postId, commentText, postElement) {
        const comment = {
            postId: postId,
            content: commentText,
            author: {
                uid: currentUser.uid,
                displayName: currentUser.displayName || 'Anonymous',
                photoURL: currentUser.photoURL || 'images/time.jpeg'
            },
            timestamp: firebase.firestore.FieldValue.serverTimestamp()
        };
        
        return db.collection('comments').add(comment)
            .then(() => {
                return db.collection('posts').doc(postId).update({
                    comments: firebase.firestore.FieldValue.increment(1)
                });
            })
            .then(() => {
                // Update UI
                const commentsContainer = postElement.querySelector('.comments');
                if (!commentsContainer) {
                    // Create comments container if it doesn't exist
                    const newCommentsContainer = document.createElement('div');
                    newCommentsContainer.className = 'comments mt-3';
                    
                    const actionsDiv = postElement.querySelector('.post-actions');
                    actionsDiv.parentNode.insertBefore(newCommentsContainer, actionsDiv.nextSibling);
                    
                    addCommentToUI(newCommentsContainer, comment);
                } else {
                    addCommentToUI(commentsContainer, comment);
                }
                
                // Update comment count in UI
                const commentCount = postElement.querySelector('.post-stats span:nth-child(2)');
                if (commentCount) {
                    const currentComments = parseInt(commentCount.textContent.match(/\\d+/)[0]);
                    commentCount.innerHTML = `<i class="fas fa-comment me-1"></i> ${currentComments + 1}`;
                }
            });
    }
    
    // Follow Barber Brothers Media channel
    function followChannel() {
        return db.collection('followers').add({
            userId: currentUser.uid,
            channelId: 'barber-brothers-media',
            timestamp: firebase.firestore.FieldValue.serverTimestamp()
        });
    }
    
    // Unfollow Barber Brothers Media channel
    function unfollowChannel() {
        return db.collection('followers')
            .where('userId', '==', currentUser.uid)
            .where('channelId', '==', 'barber-brothers-media')
            .get()
            .then(querySnapshot => {
                if (!querySnapshot.empty) {
                    return querySnapshot.docs[0].ref.delete();
                }
            });
    }
    
    // Check if user is following channel
    function checkIfFollowing() {
        if (!currentUser) return Promise.resolve(false);
        
        return db.collection('followers')
            .where('userId', '==', currentUser.uid)
            .where('channelId', '==', 'barber-brothers-media')
            .get()
            .then(querySnapshot => {
                return !querySnapshot.empty;
            });
    }
    
    // Update follower count in UI
    function updateFollowerCount(change) {
        const followerCount = document.querySelector('.social-stats span:nth-child(2) strong');
        if (followerCount) {
            const currentCount = parseFloat(followerCount.textContent);
            followerCount.textContent = (currentCount + (change * 0.001)).toFixed(1) + 'K';
        }
    }
    
    // Helper Functions
    
    // Update UI for signed in user
    function updateUIForSignedInUser(user) {
        if (loginSection) loginSection.classList.add('d-none');
        if (postSection) postSection.classList.remove('d-none');
        
        // Add user profile menu if it doesn't exist
        if (!document.getElementById('user-profile-menu')) {
            const userMenu = document.createElement('div');
            userMenu.id = 'user-profile-menu';
            userMenu.className = 'user-profile-menu d-flex align-items-center mt-3';
            userMenu.innerHTML = `
                <img src="${user.photoURL || 'images/time.jpeg'}" alt="${user.displayName || 'User'}" class="user-avatar me-2" width="40" height="40">
                <div>
                    <h6 class="mb-0">${user.displayName || 'Anonymous'}</h6>
                    <button id="sign-out-button" class="btn btn-link text-danger p-0">Sign Out</button>
                </div>
            `;
            
            if (loginSection && loginSection.parentNode) {
                loginSection.parentNode.insertBefore(userMenu, loginSection);
            }
        }
        
        // Check if user is following and update UI
        checkIfFollowing()
            .then(isFollowing => {
                if (isFollowing && followButton) {
                    followButton.textContent = 'Following';
                    followButton.classList.remove('btn-danger');
                    followButton.classList.add('btn-outline-danger');
                }
            });
    }
    
    // Update UI for signed out user
    function updateUIForSignedOutUser() {
        if (loginSection) loginSection.classList.remove('d-none');
        if (postSection) postSection.classList.add('d-none');
        
        // Remove user profile menu
        const userMenu = document.getElementById('user-profile-menu');
        if (userMenu) userMenu.remove();
        
        // Reset follow button
        if (followButton) {
            followButton.textContent = 'Follow';
            followButton.classList.remove('btn-outline-danger');
            followButton.classList.add('btn-danger');
        }
    }
    
    // Create a post element from post data
    function createPostElement(post) {
        const postElement = document.createElement('div');
        postElement.className = 'card social-post mb-4 bg-black text-white';
        postElement.dataset.postId = post.id;
        
        // Format timestamp
        let timeStr = 'Just now';
        if (post.timestamp) {
            const postTime = post.timestamp.toDate();
            const now = new Date();
            const timeDiff = Math.floor((now - postTime) / 1000 / 60); // minutes
            
            if (timeDiff < 1) {
                timeStr = 'Just now';
            } else if (timeDiff < 60) {
                timeStr = `${timeDiff} minute${timeDiff === 1 ? '' : 's'} ago`;
            } else if (timeDiff < 1440) {
                const hours = Math.floor(timeDiff / 60);
                timeStr = `${hours} hour${hours === 1 ? '' : 's'} ago`;
            } else {
                const days = Math.floor(timeDiff / 1440);
                timeStr = `${days} day${days === 1 ? '' : 's'} ago`;
            }
        }
        
        postElement.innerHTML = `
            <div class="card-header post-header border-0 bg-black">
                <div class="d-flex align-items-center">
                    <img src="${post.author.photoURL}" alt="${post.author.displayName}" class="user-avatar me-2" width="40" height="40">
                    <div>
                        <h6 class="mb-0">${post.author.displayName}</h6>
                        <small class="text-muted">Posted ${timeStr}</small>
                    </div>
                    <div class="dropdown ms-auto">
                        <button class="btn btn-link text-white" data-bs-toggle="dropdown">
                            <i class="fas fa-ellipsis-v"></i>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-dark">
                            ${currentUser && post.author.uid === currentUser.uid ? 
                                `<li><a class="dropdown-item" href="#" data-action="edit-post">Edit Post</a></li>
                                <li><a class="dropdown-item" href="#" data-action="delete-post">Delete</a></li>` :
                                `<li><a class="dropdown-item" href="#" data-action="save-post">Save Post</a></li>
                                <li><a class="dropdown-item" href="#" data-action="report-post">Report</a></li>`
                            }
                        </ul>
                    </div>
                </div>
            </div>
            <div class="card-body pb-2">
                <p class="card-text">${post.content}</p>
                ${post.imageUrl ? `<img src="${post.imageUrl}" alt="Post image" class="img-fluid rounded mb-3 post-image">` : ''}
                <div class="post-stats d-flex align-items-center text-muted mb-3">
                    <span><i class="fas fa-heart text-danger me-1"></i> ${post.likes || 0}</span>
                    <span class="ms-3"><i class="fas fa-comment me-1"></i> ${post.comments || 0}</span>
                    <span class="ms-3"><i class="fas fa-share me-1"></i> ${post.shares || 0}</span>
                </div>
                <hr class="my-2">
                <div class="post-actions d-flex">
                    <button class="btn btn-link text-white flex-grow-1"><i class="far fa-heart me-1"></i> Like</button>
                    <button class="btn btn-link text-white flex-grow-1"><i class="far fa-comment me-1"></i> Comment</button>
                    <button class="btn btn-link text-white flex-grow-1"><i class="far fa-share-square me-1"></i> Share</button>
                </div>
                <!-- Comments will be loaded here -->
                <!-- Add comment input -->
                <div class="add-comment d-flex mt-3">
                    <img src="${currentUser ? currentUser.photoURL || 'images/time.jpeg' : 'images/time.jpeg'}" 
                         alt="${currentUser ? currentUser.displayName || 'User' : 'User'}" 
                         class="user-avatar-sm me-2" width="30" height="30">
                    <input type="text" class="form-control form-control-sm bg-dark text-white" 
                           placeholder="${currentUser ? 'Write a comment...' : 'Sign in to comment'}"
                           ${!currentUser ? 'disabled' : ''}>
                </div>
            </div>
        `;
        
        // Load comments for this post
        if (post.comments > 0) {
            loadCommentsForPost(post.id, postElement);
        }
        
        // Check if current user has liked this post
        if (currentUser) {
            db.collection('likes')
                .where('postId', '==', post.id)
                .where('userId', '==', currentUser.uid)
                .get()
                .then(querySnapshot => {
                    if (!querySnapshot.empty) {
                        // User has liked this post
                        const likeButton = postElement.querySelector('.post-actions .btn-link:first-child');
                        const likeIcon = likeButton.querySelector('i');
                        likeIcon.classList.remove('far');
                        likeIcon.classList.add('fas', 'text-danger');
                    }
                });
        }
        
        return postElement;
    }
    
    // Load comments for a specific post
    function loadCommentsForPost(postId, postElement) {
        db.collection('comments')
            .where('postId', '==', postId)
            .orderBy('timestamp', 'asc')
            .limit(5) // Limit to 5 most recent comments
            .get()
            .then(querySnapshot => {
                if (querySnapshot.empty) return;
                
                // Create comments container if it doesn't exist
                let commentsContainer = postElement.querySelector('.comments');
                if (!commentsContainer) {
                    commentsContainer = document.createElement('div');
                    commentsContainer.className = 'comments mt-3';
                    
                    const addCommentDiv = postElement.querySelector('.add-comment');
                    addCommentDiv.parentNode.insertBefore(commentsContainer, addCommentDiv);
                }
                
                querySnapshot.forEach(doc => {
                    const commentData = doc.data();
                    addCommentToUI(commentsContainer, commentData);
                });
            });
    }
    
    // Add a comment to the UI
    function addCommentToUI(commentsContainer, comment) {
        const commentElement = document.createElement('div');
        commentElement.className = 'comment d-flex mb-3';
        
        // Format timestamp
        let timeStr = 'Just now';
        if (comment.timestamp) {
            const commentTime = comment.timestamp.toDate();
            const now = new Date();
            const timeDiff = Math.floor((now - commentTime) / 1000 / 60); // minutes
            
            if (timeDiff < 1) {
                timeStr = 'Just now';
            } else if (timeDiff < 60) {
                timeStr = `${timeDiff}m`;
            } else if (timeDiff < 1440) {
                const hours = Math.floor(timeDiff / 60);
                timeStr = `${hours}h`;
            } else {
                const days = Math.floor(timeDiff / 1440);
                timeStr = `${days}d`;
            }
        }
        
        commentElement.innerHTML = `
            <img src="${comment.author.photoURL}" alt="${comment.author.displayName}" class="user-avatar-sm me-2" width="30" height="30">
            <div class="comment-content bg-secondary bg-opacity-25 p-2 rounded flex-grow-1">
                <h6 class="mb-0 small">${comment.author.displayName}</h6>
                <p class="small mb-1">${comment.content}</p>
                <div class="comment-actions small">
                    <span class="text-muted">${timeStr}</span>
                    <a href="#" class="text-white ms-2">Like</a>
                    <a href="#" class="text-white ms-2">Reply</a>
                </div>
            </div>
        `;
        
        commentsContainer.appendChild(commentElement);
    }
    
    // Create toast notification
    function showToast(message) {
        // Check if toast container exists, create if not
        let toastContainer = document.querySelector('.toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
            document.body.appendChild(toastContainer);
        }
        
        // Create toast element
        const toastId = 'toast-' + Date.now();
        const toastEl = document.createElement('div');
        toastEl.className = 'toast';
        toastEl.id = toastId;
        toastEl.setAttribute('role', 'alert');
        toastEl.setAttribute('aria-live', 'assertive');
        toastEl.setAttribute('aria-atomic', 'true');
        
        toastEl.innerHTML = `
            <div class="toast-header">
                <strong class="me-auto">Barber Brothers Media</strong>
                <small>Just now</small>
                <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
            <div class="toast-body">
                ${message}
            </div>
        `;
        
        // Add to container
        toastContainer.appendChild(toastEl);
        
        // Initialize and show the toast
        const toast = new bootstrap.Toast(toastEl);
        toast.show();
        
        // Remove after it's hidden
        toastEl.addEventListener('hidden.bs.toast', function() {
            toastEl.remove();
        });
    }
});
